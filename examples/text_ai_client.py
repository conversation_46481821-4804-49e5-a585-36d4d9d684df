# coding = utf-8
import websockets
import ujson
import asyncio
from datetime import datetime, timezone


class RealTimeTextClient:
    def __init__(self, server_host: str = "127.0.0.1:8000", user_input: dict = None):
        self.is_waiting_response = False
        self.response_lock = asyncio.Lock()  # 新增异步锁
        self.server_host = server_host
        self.user_input = user_input

    async def connect(self, session_id):
        uri = f"ws://{self.server_host}/ws/streaming/ai/text/{session_id}"
        self.websocket = await websockets.connect(uri)

    async def read_user_input(self):
        """异步读取用户输入"""
        while True:
            async with self.response_lock:  # 使用锁同步状态检查
                if not self.is_waiting_response:
                    text = await asyncio.get_event_loop().run_in_executor(
                        None, input, "\n💬 : "
                    )
                    if text.strip():
                        # 直接格式化时间戳
                        iso_timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%fZ")
                        self.user_input["timestamp"] = iso_timestamp
                        await self.websocket.send(ujson.dumps({
                            "text": text,
                            "user_input": self.user_input
                        }))
                        self.is_waiting_response = True
                else:
                    await asyncio.sleep(0.1)

    async def send_text(self):
        # 并行运行输入和接收任务
        await asyncio.gather(
            self.read_user_input(),
            self.receive_messages()
        )

    async def receive_messages(self):
        """独立处理服务器消息的协程"""
        try:
            async for message in self.websocket:
                data = ujson.loads(message)
                
                if data["type"] == "start":
                    await self._start_ai_response()
                    
                elif data["type"] == "text":
                    await self._update_ai_response(data["content"])

                elif data["type"] == "cmd":
                    await self._update_cmd(data["content"])
                
                elif data["type"] == "end":
                    await self._finalize_ai_response()
                    
        except websockets.exceptions.ConnectionClosed:
            print("连接已关闭")
        except Exception as e:
            print(f"接收消息错误: {e}")

    async def _start_ai_response(self):
        async with self.response_lock:  # 改为异步上下文管理器
            print("\n🤖: ", end='', flush=True)

    async def _update_ai_response(self, chunk):
        if chunk:
            async with self.response_lock:  # 改为异步上下文管理器
                print(f"{chunk}", end='', flush=True)
    
    async def _update_cmd(self, result):
        if result:
            async with self.response_lock:  # 改为异步上下文管理器
                score_str = f"{result.get('score', 0)*100:.2f}%" if "score" in result and result["score"] else "ai"
                print(f"意图：{result['intent']}，置信度：{score_str}，实体：{result['entities']}\n", end='', flush=True)

    async def _finalize_ai_response(self):
        async with self.response_lock:  # 改为异步上下文管理器
            print("\n\n ----------------- \n")
            self.is_waiting_response = False
        
    async def run(self):
        print("开始连接服务器")
        # 获取用户输入的session_id
        session_id = input("请输入session_id: ").strip()
        await self.connect(session_id)
        print("输入消息后按回车发送")
        await self.send_text()


if __name__ == "__main__":
    client = RealTimeTextClient()
    asyncio.get_event_loop().run_until_complete(client.run())
