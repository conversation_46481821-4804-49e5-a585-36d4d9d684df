# coding = utf-8
import os
import certifi
import ssl
import urllib.request
from RealtimeSTT import AudioToTextRecorder

def process_text(text):
    print(text)

if __name__ == '__main__':
    # 设置证书路径
    os.environ['SSL_CERT_FILE'] = certifi.where()
    
    # 创建一个使用系统证书的SSL上下文
    ssl_context = ssl.create_default_context(cafile=certifi.where())
    
    # Monkey Patch whisper 的 _download 方法
    original_urlopen = urllib.request.urlopen
    urllib.request.urlopen = lambda *args, **kwargs: original_urlopen(*args, context=ssl_context, **kwargs)

    print("Wait until it says 'speak now'")
    recorder = AudioToTextRecorder()

    while True:
        recorder.text(process_text)