package main

import (
	"log"
	"net/http"
	"net/http/httputil"
	"net/url"

	"github.com/gin-gonic/gin"
)

func main() {
	targetUrl, err := url.Parse("http://127.0.0.1:8000")
	proxy := httputil.NewSingleHostReverseProxy(targetUrl)
	proxy.FlushInterval = -1
	proxy.ErrorHandler = func(w http.ResponseWriter, r *http.Request, err error) {
		log.Printf("Error: %v", err)
		http.Error(w, "Something went wrong", 500)
	}
	engine := gin.New()

	engine.POST("/streaming/ai/text/:sessId", func(c *gin.Context) {

		if err != nil {
			log.Printf("Error: %v", err)
			c.JSON(500, gin.H{"error": "Something went wrong"})
			return
		}

		c.Writer.Header().Set("Content-Type", "text/event-stream")
		c.<PERSON>.Header().Set("Cache-Control", "no-cache")
		c.Writer.Header().Set("Connection", "keep-alive")
		proxy.ServeHTTP(c.Writer, c.Request)
	})

	engine.GET("/ws/streaming/ai/text/:sessId", func(c *gin.Context) {
		proxy.ServeHTTP(c.Writer, c.Request)
	})

	engine.Run("0.0.0.0:12138")

}
