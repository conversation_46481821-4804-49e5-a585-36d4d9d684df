# coding = utf-8
import asyncio
import geocoder
import uuid
from geopy.geocoders import Nominatim
from geopy.exc import GeocoderT<PERSON>Out, GeocoderServiceError
from timezonefinder import TimezoneFinder

from audio_ai_client import RealTimeASRClient
from text_ai_client import RealTimeTextClient

# 服务器配置
LOCAL_SERVER_HOST = "127.0.0.1:8000"
REMOTE_SERVER_HOST = "**************"
PROXY_SERVER_HOST = "127.0.0.1:12138"


class AIClientSelector:
    def __init__(self):
        self.server_host = LOCAL_SERVER_HOST
        self.mode = None
        self.session_id = None

    async def run(self):
        print("🌐 欢迎使用AI对话客户端")
        print("=" * 40)

        # 服务器选择
        print("\n请选择服务器：")
        server_choice = input(
            f"""1. 本地服务器 ({LOCAL_SERVER_HOST}) [默认]
2. 远程服务器 ({REMOTE_SERVER_HOST})
3. 代理服务器 ({PROXY_SERVER_HOST})
请选择 (1/2/3，回车选择默认): """
        ).strip()
        if server_choice == "2":
            self.server_host = REMOTE_SERVER_HOST
        elif server_choice == "3":
            self.server_host = PROXY_SERVER_HOST
        else:
            self.server_host = LOCAL_SERVER_HOST

        print(f"🖥 已选择服务器：{self.server_host}")

        # 模式选择
        print("\n请选择对话模式：")
        while not self.mode:
            choice = input("1. 文本对话 [默认]\n2. 语音对话\n请选择 (1/2，回车选择默认): ").strip()
            if not choice:
                choice = "1"
            self.mode = "text" if choice == "1" else "audio" if choice == "2" else None
            if not self.mode:
                print("⚠️ 无效选项，请重新输入")
        
        user_input = {}

        # 获取当前位置
        g = geocoder.ip("me")
        default_lat = g.lat
        default_lng = g.lng
        print(f"\n🌐 当前位置：{default_lat}, {default_lng}")

        # 用户确认或输入新坐标
        use_default = input("是否使用当前定位？[Y/n，回车选择默认] ").strip().lower()
        if use_default == 'n':
            while True:
                try:
                    new_lat = float(input("请输入纬度（例如39.9042）：").strip())
                    new_lng = float(input("请输入经度（例如116.4074）：").strip())
                    default_lat = new_lat
                    default_lng = new_lng
                    break
                except ValueError:
                    print("⚠️ 输入无效，请输入有效的数字")

        print(f"📍 最终使用位置：{default_lat}, {default_lng}")

        # 显示原始语言地址信息
        location_info = await self.reverse_geocode(default_lat, default_lng)
        location_info["latitude"] = default_lat
        location_info["longitude"] = default_lng
        user_input["location"] = location_info

        # 获取时区
        tf = TimezoneFinder()
        timezone = tf.timezone_at(lat=default_lat, lng=default_lng)
        print(f"\n 🌐 时区: {timezone}")
        user_input["timezone"] = timezone

        # 修改后的语言选择部分
        print("\n请选择语言：")
        lang_choice = input("1. 英文\n2. 中文\n其他或不输入为不指定 [默认]\n请选择 (1/2，回车选择默认): ").strip()
        if lang_choice == "1":
            user_input["language"] = "en"
        elif lang_choice == "2":
            user_input["language"] = "zh"
        else:
            user_input["language"] = ""
        print(f"🗣 使用语言: {user_input['language'] if user_input['language'] else '不指定'}")

        # 获取session_id
        print("\n🔑 会话ID（直接回车将自动生成随机ID）")
        while True:
            session_input = input("请输入会话ID或直接回车生成: ").strip()
            if session_input:
                self.session_id = session_input
                break
            else:
                self.session_id = str(uuid.uuid4())
                print(f"🆔 已自动生成会话ID: {self.session_id}")
                break

        # 根据模式启动对应客户端
        if self.mode == "text":
            await self.run_text_client(user_input=user_input)
        else:
            await self.run_audio_client(user_input=user_input)

    async def run_text_client(self, user_input: dict = None):
        """运行文本客户端"""
        client = RealTimeTextClient(server_host=self.server_host, user_input=user_input)
        print("\n📝 文本模式已就绪（输入消息后按回车发送）")
        await client.connect(self.session_id)
        await client.send_text()

    # TODO: 支持user_input
    async def run_audio_client(self):
        """运行语音客户端"""
        client = RealTimeASRClient(server_host=self.server_host)
        print("\n🎙 语音模式已就绪，请开始说话...")
        await client.connect(self.session_id)
        client.start_recording()
        await client.send_audio()

    async def reverse_geocode(self, lat: float, lng: float):
        """反向地理编码查询地址信息"""
        location_info = {}
        try:
            geolocator = Nominatim(user_agent="ai_client_app")
            location = await asyncio.to_thread(
                geolocator.reverse, 
                (lat, lng),
                exactly_one=True
            )
            
            # 固定使用中文标签
            labels = [
                ("国家", "country", "country"),
                ("省/州", "state", "state"),
                ("城市", ["city", "town", "village"], "city"),
                ("区县", "county", "county"),
                ("街道", "road", "street"),
            ]
            
            if location:
                print("\n🌍 地址解析结果：")
                address = location.raw.get('address', {})
                
                for label, keys, key in labels:
                    if isinstance(keys, list):
                        value = next((address.get(k) for k in keys if address.get(k)), "")
                    elif keys == "address":
                        value = location.address
                    else:
                        value = address.get(keys, "")
                    print(f"  {label}: {value}")
                    location_info[key] = value
                    
            else:
                print("⚠️ 无法解析该位置的地址信息")
                
        except (GeocoderTimedOut, GeocoderServiceError):
            print("⚠️ 地理编码服务暂时不可用，请稍后再试")
        except Exception as e:
            print(f"⚠️ 地址解析发生错误: {str(e)}")
        return location_info


if __name__ == "__main__":
    selector = AIClientSelector()
    try:
        asyncio.get_event_loop().run_until_complete(selector.run())
    except KeyboardInterrupt:
        print("\n👋 已退出程序")
