# coding = utf-8
import pyaudio
import websockets
import ujson
import webrtcvad
import asyncio


# 音频参数
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 16000  # Whisper标准输入频率
CHUNK_DURATION = 0.03  # 使用30ms的帧长度，这是webrtcvad推荐的帧长度
CHUNK_SIZE = int(RATE * CHUNK_DURATION)  # 480个采样点

class RealTimeASRClient:
    def __init__(self, server_host: str = "127.0.0.1:8000"):
        self.audio = pyaudio.PyAudio()
        self.stream = None
        self.vad = webrtcvad.Vad(3)  # aggressiveness level 3 (0-3)
        self.buffer = bytearray()  # 改为动态增长的字节数组
        self.silence_duration = 0  # 持续静音时间
        self.SILENCE_THRESHOLD = 1.0  # 静音持续1秒认为语句结束
        self.MIN_SPEECH_DURATION = 0.3  # 最短有效语音时长
        self.is_recording = False  # 是否正在录音状态
        self.server_host = server_host

    async def connect(self, session_id):
        uri = f"ws://{self.server_host}/ws/streaming/ai/audio/{session_id}"
        self.websocket = await websockets.connect(uri)
        
    def start_recording(self):
        self.stream = self.audio.open(
            format=FORMAT,
            channels=CHANNELS,
            rate=RATE,
            input=True,
            frames_per_buffer=CHUNK_SIZE,
            stream_callback=self._audio_callback
        )
        
    def _audio_callback(self, in_data, frame_count, time_info, status):
        try:
            is_speech = self.vad.is_speech(in_data, RATE)
            
            if is_speech:
                self.silence_duration = 0  # 重置静音计时
                self.is_recording = True
                self.buffer.extend(in_data)  # 持续累积语音数据
            elif self.is_recording:
                # 累积静音时间（每次回调间隔是CHUNK_DURATION）
                self.silence_duration += CHUNK_DURATION
                
                # 如果静音超过阈值，结束录音
                if self.silence_duration >= self.SILENCE_THRESHOLD:
                    self.is_recording = False
        except Exception as e:
            print(f"VAD处理错误: {e}")
        return (in_data, pyaudio.paContinue)
    
    async def send_audio(self):
        while True:
            try:
                
                if not self.is_recording and len(self.buffer) > 0:
                    audio_duration = len(self.buffer) / (RATE * 2)
                    # 临时换行显示音频时长
                    print(f"\nAudio duration: {audio_duration:.2f}s", flush=True)
                    
                    if audio_duration >= self.MIN_SPEECH_DURATION:
                        chunk = bytes(self.buffer)
                        await self.websocket.send(chunk)
                        
                        # 创建单独的任务处理服务器响应
                        response_task = asyncio.create_task(self._handle_server_responses())
                        
                        # 等待当前录音处理完成
                        await response_task
                        
                    self.buffer.clear()
                    self.silence_duration = 0
                    
                await asyncio.sleep(CHUNK_DURATION)
            except Exception as e:
                print(f"Error: {e}")
                break

    async def _handle_server_responses(self):
        """单独处理服务器响应的协程"""
        try:
            current_response = []
            async for message in self.websocket:
                data = ujson.loads(message)
                
                if data["type"] == "transcribe":
                    self._update_transcript(data["text"])
                
                elif data["type"] == "start":
                    self._start_ai_response()
                    
                elif data["type"] == "text":
                    current_response.append(data["content"])
                    self._update_ai_response(data["content"])  
                
                elif data["type"] == "cmd":
                    await self._update_cmd(data["content"])
                
                elif data["type"] == "end":
                    self._finalize_ai_response(''.join(current_response))  
                    return  # 正常结束响应处理
                    
        except Exception as e:
            print(f"响应处理错误: {e}")

    def _update_transcript(self, text):
        if text:
            print(f"\n🗣: {text}\n", flush=True)
    
    def _start_ai_response(self):
        print("\n🤖: ", end='', flush=True)

    def _update_ai_response(self, chunk):
        if chunk:
            # 直接显示最新chunk内容，不清除之前内容
            print(f"{chunk}", end='', flush=True)
    
    async def _update_cmd(self, result):
        if result:
            async with self.response_lock:  # 改为异步上下文管理器
                print(f"意图：{result['name']}，置信度：{result['score']}，实体：{result['entities']}\n", end='', flush=True)

    def _finalize_ai_response(self, text):
        print("\n ----------------- \n")
        
    async def run(self):
        print("开始连接服务器") 
        # 获取用户输入的session_id
        session_id = input("请输入session_id: ").strip()
        await self.connect(session_id)
        print("开始录音")
        self.start_recording()
        print("开始发送音频")
        await self.send_audio()


if __name__ == "__main__":
    client = RealTimeASRClient()
    asyncio.get_event_loop().run_until_complete(client.run())
