import unittest

from business.chat_session.store import Message, MessageR<PERSON>, Session<PERSON>anager, MessageManager
from config.settings import settings
import datetime
from motor.motor_asyncio import AsyncIOMotorClient
import redis.asyncio as redis
import asyncio


class TestMessageManager(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self):
        await super().asyncSetUp()
        settings.MONGO_DB_NAME = "test_session"
        self._mgo = AsyncIOMotorClient(settings.MONGO_URI)
        self._redis = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.CACHE_MESSAGE_LIST_DB,
        )
        await self._mgo.drop_database(settings.MONGO_DB_NAME)
        self._client = MessageManager(self._mgo, self._redis)
        for user_id in range(5):
            for sess_id in range(5):
                for msg_id in range(50):
                    await self._client.push_message(
                        Message(
                            session_id=f"sess{sess_id:02}",
                            user_id=f"user{user_id:02}",
                            role=MessageRole.User,
                            round_key="round_1",
                            content=f"msg_u{user_id:02}_s{sess_id:02}_m{msg_id:02}",
                        )
                    )

    async def asyncTearDown(self):
        self._mgo.close()
        await self._redis.aclose()
        return await super().asyncTearDown()

    async def test_list_message(self):
        USER = "user01"
        messages, _, _ = await self._client.search_messages(USER)
        self.assertEqual(len(messages), 20)

    async def test_list_multi_sess_msgs(self):
        USER = "user01"
        SESSION = ["sess01", "sess02"]

        MSG_CONTENT = [f"msg_u01_s{sess_id:02}_m{msg_id:02}" for sess_id in range(1, 3) for msg_id in range(50)]

        msgs, _, _ = await self._client.search_messages(USER, session_ids=SESSION, page_size=100000)
        self.assertEqual(len(msgs), 100)
        for m in msgs:
            self.assertIn(m.content, MSG_CONTENT)

    async def test_list_msg_keyword(self):
        USER = "user01"
        KEY = "msg_u01_s01"  # 所有 session1的记录

        msgs1, _, _ = await self._client.search_messages(USER, session_ids=["sess01"], page_size=100000)
        msgs2, _, _ = await self._client.search_messages(USER, message_like=KEY, page_size=100000)
        self.assertEqual(len(msgs1), 50)
        self.assertEqual(len(msgs1), len(msgs2))
        self.assertEqual(msgs1, msgs2)

    async def test_list_page_token(self):
        USER = "user01"
        SESS = "sess01"

        p1, _, _ = await self._client.search_messages(USER, session_ids=[SESS], page_size=10)
        p2, _, _ = await self._client.search_messages(USER, session_ids=[SESS], page_size=10, page_token=p1[-1].id)
        self.assertEqual(len(p1), 10)
        self.assertEqual(len(p2), 10)
        for i in range(10):
            self.assertEqual(p1[i].content, f"msg_u01_s01_m{i:02}")
        for i in range(10, 20):
            self.assertEqual(p2[i - 10].content, f"msg_u01_s01_m{i:02}")

    async def test_list_with_prev_msg(self):
        """
        prev_message 就直接类似于 grep -C

        交互逻辑类似于：
        1. 用户搜索特定关键词
        2. 点击某条消息，跳转到 session 并定位到该消息(page_token = target.id, prev=20, page_size=20)
        3. API 返回该记录的前序 20 条，后续 20 条
        """

        USER = "user01"
        SESS = "sess01"

        # 捞出所有数据
        msg, _, _ = await self._client.search_messages(
            USER,
            session_ids=[SESS],
        )
        # 找一条
        point = msg[19]

        # 用 page_token 模拟命中的中甲你消息
        msg, _, _ = await self._client.search_messages(
            USER,
            session_ids=[SESS],
            prev_messages=5,
            page_token=point.id,
            page_size=20,
        )

        self.assertEqual(len(msg), 25)  # 后续 20 + 前序 5

        for i in range(5):
            self.assertEqual(msg[i].content, f"msg_u01_s01_m{i:02}")

        for i in range(20, 40):
            self.assertEqual(msg[i - 20 + 5].content, f"msg_u01_s01_m{i:02}")
