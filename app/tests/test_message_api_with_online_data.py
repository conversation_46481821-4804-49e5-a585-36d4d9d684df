import csv
import json
import os
import unittest

from bson import ObjectId

from business.chat_session.store import Message, MessageRole, SessionManager, MessageManager
from config.settings import settings
import datetime
from motor.motor_asyncio import AsyncIOMotorClient
import redis.asyncio as redis
import asyncio

from models.message import ChatMessageQuestionOrAnswerType, MessageCategory


class TestMessageAPIWithOnlineDataManager(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self):
        settings.MONGO_DB_NAME = "test_session"
        self._mgo = AsyncIOMotorClient(settings.MONGO_URI)
        self._redis = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.CACHE_MESSAGE_LIST_DB,
        )
        await self._mgo.drop_database(settings.MONGO_DB_NAME)
        self._client = MessageManager(self._mgo, self._redis)
        await super().asyncSetUp()

    async def asyncTearDown(self):
        self._mgo.close()
        await self._redis.aclose()
        return await super().asyncTearDown()

    async def setupMessagePagingCsv(self):
        msgs = []
        idx = 0
        with open(os.path.join("app", "tests", "datas", "message_paging.csv"), newline="", encoding="utf-8") as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                msgs.append(
                    Message(
                        id=ObjectId(row["_id"].replace("ObjectID('", "").replace("')", "")),
                        round_key=row["round_key"],
                        session_id=row["session_id"],
                        user_id=str(row["user_id"]),
                        role=MessageRole(row["role"]),
                        created_at=row["created_at"],
                        name=f"ROW_{idx}",  # for debug
                        message_category=MessageCategory(row["message_category"]),
                        content=str(row["content"]),
                    )
                )
                idx += 1
        await self._mgo.drop_database(settings.MONGO_DB_NAME)
        for msg in msgs:
            await self._client.push_message(msg)
        return msgs

    async def test_paging_error(self):
        USER = "146"
        SESS = "0193255d-0d4f-76b2-91d9-84649ad98513"
        all_msgs = await self.setupMessagePagingCsv()

        PAGE_SIZE = 40  # 等价于 API 输入 20

        async def check_round(pg, expected_msg):
            msg, cnt, pg = await self._client.api_search_messages(
                USER,
                session_ids=[SESS],
                page_size=PAGE_SIZE,
                sorts=[("created_at", -1)],
                acquire_count=True,
                page_token=pg,
            )
            if len(expected_msg) != PAGE_SIZE:
                self.assertEqual(len(expected_msg) / 2, len(msg))
            else:
                self.assertEqual(len(msg), 20)
            self.assertEqual(cnt, len(all_msgs) // 2)

            for i in range(len(msg)):
                exp_q = expected_msg[-2]
                exp_a = expected_msg[-1]
                expected_msg = expected_msg[:-2]
                self.assertEqual(msg[i].question.content, exp_q.content, f"idx={i}")
                if msg[i].answer.type == ChatMessageQuestionOrAnswerType.Text:
                    self.assertEqual(msg[i].answer.content, exp_a.content, f"idx={i}")
                else:
                    intent_data = json.loads(exp_a.content)
                    self.assertEqual(msg[i].answer.intent_category, intent_data["intent"], f"idx={i}")

            return pg

        pg = await check_round(None, all_msgs[-PAGE_SIZE:])
        pg = await check_round(pg, all_msgs[-PAGE_SIZE * 2 : -PAGE_SIZE])
        pg = await check_round(pg, all_msgs[-PAGE_SIZE * 3 : -PAGE_SIZE * 2])
        pg = await check_round(pg, all_msgs[-PAGE_SIZE * 4 : -PAGE_SIZE * 3])
