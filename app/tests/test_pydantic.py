import json
import unittest

from bson import ObjectId

from models.common import JarvisBaseModel, PyObjectId


class TestPydantic(unittest.TestCase):

    def test_object_id(self):
        s = "67dc11e2dd8148873ce8df04"
        o = ObjectId(s)

        class TestModel(JarvisBaseModel):
            oid: PyObjectId

        m = TestModel(oid=o)
        json_str = m.model_dump_json()
        print("")
        print(json_str)

        m2 = TestModel(**json.loads(json_str))
        print(m2)

        self.assertIsInstance(o, ObjectId)
        self.assertEqual(m.oid, m2.oid)
        self.assertEqual(m.oid, o)
        self.assertEqual(str(m.oid), s)
