import unittest

from business.chat_session.store import Message<PERSON><PERSON><PERSON>, SessionManager
from business.chat_session.title_gen import update_session_title
from config.settings import TitleGenRule, settings
import datetime
from motor.motor_asyncio import AsyncIOMotorClient
import redis.asyncio as redis

from models.common import Context
from models.message import Message, MessageRole


class TestTitleGen(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self):
        await super().asyncSetUp()
        settings.MONGO_DB_NAME = "test_session"
        self._mgo = AsyncIOMotorClient(settings.MONGO_URI)
        self._redis = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.CACHE_MESSAGE_LIST_DB,
        )
        self._client = SessionManager(self._mgo)
        self._msg_client = MessageManager(self._mgo, self._redis)
        self._client.client.drop_database(settings.MONGO_DB_NAME)

    async def asyncTearDown(self):
        self._mgo.close()
        return await super().asyncTearDown()

    async def test_title_gen_first_quest_mode_intent(self):
        settings.SESSION_TITLE_GEN_RULE = TitleGenRule.FIRST_QUESTION

        ctx = Context(
            session_id="session_id",
            user_id="user_id",
        )
        sess = await self._client.must_get_session(ctx.session_id, ctx.user_id)

        await self._msg_client.push_message(
            Message(
                session_id=ctx.session_id,
                user_id=ctx.user_id,
                role=MessageRole.User,
                round_key="round_1",
                content="速记",
            )
        )
        await self._msg_client.push_message(
            Message(
                session_id=ctx.session_id,
                user_id=ctx.user_id,
                role=MessageRole.CustomIntent,
                round_key="round_1",
                content='{"intent": "qn_on", "entities": ["速记"]}',
            )
        )

        await update_session_title(ctx, sess, self._client, self._msg_client, None)

        self.assertEqual(sess.title, "Intent: qn on ['速记']")
