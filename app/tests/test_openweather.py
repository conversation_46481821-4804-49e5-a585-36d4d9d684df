import datetime
import unittest

from client.openweather import OpenWeatherClient
from config.settings import settings


class TestOpenWeatherClient(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        self.client = OpenWeatherClient(settings.OPENWEATHER_API_KEY)
        return super().setUp()

    async def test_get_weather(self):
        # 杭州
        resp = await self.client.get_current_weather("Hangzhou")
        self.assertIsNotNone(resp)
        print(resp)

    async def test_get_forecast_weather(self):
        # 杭州
        resp = await self.client.get_forecast_weather(
            "Hangzhou",
            datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=1),
        )
        self.assertIsNotNone(resp)
        print(resp)
