import unittest

from client.searxng import SearXNGClient
from config.settings import settings


class TestSearxngClient(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        self.client = SearXNGClient(settings.SEARXNG_URL)
        return super().setUp()

    async def test_search(self):
        # 杭州
        resp = await self.client.query("中国历史")
        self.assertIsNotNone(resp)
        print(resp)
