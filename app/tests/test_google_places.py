import datetime
import unittest

from client.google_places import GooglePlacesClient
from config.settings import settings
import geocoder


class TestGooglePlacesClient(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        self.client = GooglePlacesClient(settings.GOOGLE_PLACES_API_KEY)
        location = geocoder.ip("me")
        self.lat = location.lat
        self.lng = location.lng
        return super().setUp()

    async def test_search_nearby(self):
        included_types = [
            "restaurant",
            "school",
        ]
        resp = await self.client.search_nearby(
            included_types,
            center_latitude=self.lat,
            center_longitude=self.lng,
            radius_meter=settings.SEARCH_NEARBY_RADIUS_METER,
        )
        self.assertIsNotNone(resp)
        print(resp)

    async def test_search(self):
        query = "ChainRestaurant New York"
        resp = await self.client.search_text(query)
        self.assertIsNotNone(resp)
        self.assertIsInstance(resp, list)
        self.assertGreater(len(resp), 0)
        print(resp)
