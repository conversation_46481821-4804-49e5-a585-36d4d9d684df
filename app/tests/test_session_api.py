import unittest

from business.chat_session.store import SessionManager
from config.settings import settings
import datetime
from motor.motor_asyncio import AsyncIOMotorClient


class TestSessionManager(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self):
        await super().asyncSetUp()
        settings.MONGO_DB_NAME = "test_session"
        self._mgo = AsyncIOMotorClient(settings.MONGO_URI)
        self._client = SessionManager(self._mgo)
        self._client.client.drop_database(settings.MONGO_DB_NAME)
        for user_id in range(5):
            for sess_id in range(100):
                # sess00, sess01, ...
                await self._client.must_get_session(f"sess{sess_id:02}", f"user{user_id}")

    async def asyncTearDown(self):
        self._mgo.close()
        await self._redis.aclose()
        return await super().asyncTearDown()

    async def test_session_title_update(self):
        USER = "user1"
        for i in range(10):
            await self._client.update_session_title(USER, f"sess{i:02}", f"title_{i}")
        sessions, _ = await self._client.search_session(USER)
        for sess in sessions:
            if sess.session_id in [f"sess{i:02}" for i in range(10)]:
                self.assertIn(sess.title, [f"title_{i}" for i in range(10)])

    async def test_session_list_simple(self):
        USER = "user1"
        sessions, _ = await self._client.search_session(USER)
        self.assertEqual(len(sessions), 20)  # default

    async def test_session_list_with_title(self):
        USER = "user1"
        SESS = "sess01"

        now = datetime.datetime.now()
        TITLE = now.strftime("%Y-%m-%d %H:%M:%S")

        await self._client.update_session_title(USER, SESS, TITLE)
        sessions, _ = await self._client.search_session(USER, title=TITLE)
        self.assertEqual(len(sessions), 1)
        self.assertEqual(sessions[0].title, TITLE)

    async def test_session_list_with_title_like(self):
        USER = "user1"
        SESS = "sess01"

        now = datetime.datetime.now()
        TITLE = now.strftime("%Y-%m-%d %H:%M:%S")

        prefix = now.strftime("%Y-%m-%d")
        suffix = now.strftime("%H:%M:%S")
        mid = now.strftime("-%d %H")

        await self._client.update_session_title(USER, SESS, TITLE)

        for kw in [prefix, suffix, mid]:
            sessions, _ = await self._client.search_session(USER, title_like=kw)
            self.assertEqual(len(sessions), 1)
            self.assertEqual(sessions[0].title, TITLE)

    async def test_session_list_with_page(self):
        USER = "user1"
        sessions, _ = await self._client.search_session(USER, page_size=20)
        self.assertEqual(len(sessions), 20)
        set([x.session_id for x in sessions]) == set([f"sess{i:02}" for i in range(0, 20)])

        sessions, _ = await self._client.search_session(USER, page_size=20, page_token=sessions[-1].id)
        self.assertEqual(len(sessions), 20)
        set([x.session_id for x in sessions]) == set([f"sess{i:02}" for i in range(20, 40)])

    async def test_session_sort_by_session(self):
        USER = "user1"
        sessions, _ = await self._client.search_session(USER, sorts=[("session_id", 1)])
        self.assertEqual(len(sessions), 20)
        self.assertEqual(sessions[0].session_id, "sess00")
        self.assertEqual(sessions[-1].session_id, "sess19")

        sessions, _ = await self._client.search_session(USER, sorts=[("session_id", -1)])
        self.assertEqual(len(sessions), 20)
        self.assertEqual(sessions[0].session_id, "sess99")
        self.assertEqual(sessions[-1].session_id, "sess80")
