import unittest

from business.chat_session.store import Message, Message<PERSON><PERSON>, Session<PERSON><PERSON><PERSON>, MessageManager
from config.settings import settings
import datetime
from motor.motor_asyncio import AsyncIOMotorClient
import redis.asyncio as redis
import asyncio


class TestMessageManager(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self):
        await super().asyncSetUp()
        self._mgo = AsyncIOMotorClient(settings.MONGO_URI)
        self._redis = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.CACHE_MESSAGE_LIST_DB,
        )
        self._client = MessageManager(self._mgo, self._redis)

    async def asyncTearDown(self):
        self._mgo.close()
        await self._redis.aclose()
        return await super().asyncTearDown()

    async def test_list_message_xq(self):
        ret = await self._client.api_search_messages("19281", ["01965c3c-97b3-7d11-8ae0-fa33bd790759"], page_size=500)
        print(ret)
