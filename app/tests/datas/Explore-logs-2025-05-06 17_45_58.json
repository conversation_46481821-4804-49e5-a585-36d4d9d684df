[{"line": "{\"name\": \"jarvis.business.ai.ai_agent\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:43:02+0000\", \"exc_info\": null, \"filename\": \"ai_agent.py\", \"funcName\": \"process_stream\", \"levelname\": \"INFO\", \"lineno\": 206, \"message\": \"[TRACE] GEMINI(gemini-2.0-flash) input[ Ladies news about <PERSON>](5) | [NLP INTENT] miss[0.02s] | first(4.93s)(2) | full(6.29s)(146) | round #2\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\", \"backend_name\": \"gemini\", \"backend_model\": \"gemini-2.0-flash\", \"input\": \" Ladies news about <PERSON>\", \"input_len\": 5, \"response_len\": 146, \"response\": \"Here's a summary of recent news regarding <PERSON> and women:\\n\\n*   **Women's Issues & Policies:** <PERSON> has signed an executive order recognizing women as biologically female. Some policies of his\", \"response_type\": \"text_ai\", \"round_key\": \"f2fe147355a3519a9f6070c90b0454d4\"}", "timestamp": "1746524582688583543", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.tools.base\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:43:00+0000\", \"exc_info\": null, \"filename\": \"base.py\", \"funcName\": \"run\", \"levelname\": \"INFO\", \"lineno\": 48, \"message\": \"CallTool: search_general took 3.60s\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\", \"tool\": \"search_general\", \"arguments\": [[], {\"keywords\": \"<PERSON> ladies news\"}]}", "timestamp": "1746524580684353066", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.tools.search_general\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:42:57+0000\", \"exc_info\": null, \"filename\": \"search_general.py\", \"funcName\": \"call\", \"levelname\": \"INFO\", \"lineno\": 39, \"message\": \"Tool called: GeneralSearch | keywords[<PERSON> ladies news]\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\", \"tool\": \"search_general\", \"keywords\": \"<PERSON> ladies news\"}", "timestamp": "1746524577176486476", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.memory\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:42:56+0000\", \"exc_info\": null, \"filename\": \"memory.py\", \"funcName\": \"_filter_messages\", \"levelname\": \"DEBUG\", \"lineno\": 189, \"message\": \"remove 0 messages by filter\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\", \"messages\": [\"id=None round_key='adcbfa2f83baeec51c02a60a6bb458e6' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=' Who are you' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T09:41:53.738946+00:00'\", \"id=None round_key='adcbfa2f83baeec51c02a60a6bb458e6' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content=\\\"I'm Even AI, a sophisticated and quick-witted AI assistant built by Even Realities.\\\" tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T09:41:54.708565+00:00'\", \"id=None round_key='f2fe147355a3519a9f6070c90b0454d4' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=' Ladies news about Donald Trump' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T09:42:56.371142+00:00'\"], \"remove_round_keys\": \"\"}", "timestamp": "1746524576425749879", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.memory\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:42:56+0000\", \"exc_info\": null, \"filename\": \"memory.py\", \"funcName\": \"get\", \"levelname\": \"DEBUG\", \"lineno\": 67, \"message\": \"Loaded history 3 messages\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\", \"messages\": [\"id=None round_key='adcbfa2f83baeec51c02a60a6bb458e6' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=' Who are you' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T09:41:53.738946+00:00'\", \"id=None round_key='adcbfa2f83baeec51c02a60a6bb458e6' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content=\\\"I'm Even AI, a sophisticated and quick-witted AI assistant built by Even Realities.\\\" tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T09:41:54.708565+00:00'\", \"id=None round_key='f2fe147355a3519a9f6070c90b0454d4' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=' Ladies news about Donald Trump' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T09:42:56.371142+00:00'\"]}", "timestamp": "1746524576425666397", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.memory\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:42:56+0000\", \"exc_info\": null, \"filename\": \"memory.py\", \"funcName\": \"get\", \"levelname\": \"DEBUG\", \"lineno\": 48, \"message\": \"Getting history messages\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\"}", "timestamp": "1746524576425562081", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.ai_agent\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:41:54+0000\", \"exc_info\": null, \"filename\": \"ai_agent.py\", \"funcName\": \"process_stream\", \"levelname\": \"INFO\", \"lineno\": 206, \"message\": \"[TRACE] GEMINI(gemini-2.0-flash) input[ Who are you](3) | [NLP INTENT] miss[0.02s] | first(0.71s)(1) | full(0.99s)(13) | round #1\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\", \"backend_name\": \"gemini\", \"backend_model\": \"gemini-2.0-flash\", \"input\": \" Who are you\", \"input_len\": 3, \"response_len\": 13, \"response\": \"I'm Even AI, a sophisticated and quick-witted AI assistant built by Even Realities.\", \"response_type\": \"text_ai\", \"round_key\": \"adcbfa2f83baeec51c02a60a6bb458e6\"}", "timestamp": "1746524514783434124", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.memory\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:41:53+0000\", \"exc_info\": null, \"filename\": \"memory.py\", \"funcName\": \"_filter_messages\", \"levelname\": \"DEBUG\", \"lineno\": 189, \"message\": \"remove 0 messages by filter\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\", \"messages\": [\"id=None round_key='adcbfa2f83baeec51c02a60a6bb458e6' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=' Who are you' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T09:41:53.738946+00:00'\"], \"remove_round_keys\": \"\"}", "timestamp": "1746524513781601308", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.memory\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:41:53+0000\", \"exc_info\": null, \"filename\": \"memory.py\", \"funcName\": \"get\", \"levelname\": \"DEBUG\", \"lineno\": 67, \"message\": \"Loaded history 1 messages\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\", \"messages\": [\"id=None round_key='adcbfa2f83baeec51c02a60a6bb458e6' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=' Who are you' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T09:41:53.738946+00:00'\"]}", "timestamp": "1746524513781545454", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.memory\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:41:53+0000\", \"exc_info\": null, \"filename\": \"memory.py\", \"funcName\": \"get\", \"levelname\": \"DEBUG\", \"lineno\": 48, \"message\": \"Getting history messages\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\"}", "timestamp": "1746524513781458618", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.ai_agent\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:41:26+0000\", \"exc_info\": null, \"filename\": \"ai_agent.py\", \"funcName\": \"process_stream\", \"levelname\": \"INFO\", \"lineno\": 206, \"message\": \"[TRACE] GEMINI(gemini-2.0-flash) input[Hey even who are you](5) | [NLP INTENT] miss[0.09s] | full(0.16s)(0) | round #1\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\", \"backend_name\": \"gemini\", \"backend_model\": \"gemini-2.0-flash\", \"input\": \"Hey even who are you\", \"input_len\": 5, \"response_len\": 0, \"response\": \"\", \"response_type\": \"text_ai\", \"round_key\": \"9bcc539a366cf4b6b5b214265afe6aab\"}", "timestamp": "1746524486714571947", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.ai_agent\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:41:26+0000\", \"exc_info\": \"Traceback (most recent call last):\\n  File \\\"/ai-jarvis/app/business/ai/ai_agent.py\\\", line 157, in process_stream\\n    async for result in self._client.send_chat_stream(\\n  File \\\"/ai-jarvis/app/business/ai/backend/base.py\\\", line 41, in send_chat_stream\\n    async for chunk in self._client.send_chat_stream(\\n  File \\\"/ai-jarvis/app/business/ai/llm/gemini.py\\\", line 236, in send_chat_stream\\n    async for x in GeminiContentGenerator(\\n  File \\\"/ai-jarvis/app/business/ai/llm/llm.py\\\", line 23, in chat\\n    async for chunk in parsed_response:\\n  File \\\"/usr/local/lib/python3.10/dist-packages/google/genai/models.py\\\", line 6788, in async_generator\\n    response = await self._generate_content_stream(\\n  File \\\"/usr/local/lib/python3.10/dist-packages/google/genai/models.py\\\", line 5698, in _generate_content_stream\\n    request_dict = _GenerateContentParameters_to_mldev(\\n  File \\\"/usr/local/lib/python3.10/dist-packages/google/genai/models.py\\\", line 1145, in _GenerateContentParameters_to_mldev\\n    for item in t.t_contents(\\n  File \\\"/usr/local/lib/python3.10/dist-packages/google/genai/_transformers.py\\\", line 354, in t_contents\\n    raise ValueError('contents are required.')\\nValueError: contents are required.\", \"filename\": \"ai_agent.py\", \"funcName\": \"process_stream\", \"levelname\": \"ERROR\", \"lineno\": 189, \"message\": \"[ERROR] Failed to process AI streaming request | GEMINI(gemini-2.0-flash) input[Hey even who are you](5) | [NLP INTENT] miss[0.09s]\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\", \"err\": \"contents are required.\", \"backend_name\": \"gemini\", \"backend_model\": \"gemini-2.0-flash\", \"input\": \"Hey even who are you\", \"input_len\": 5, \"response_len\": 0, \"response\": \"\", \"response_type\": \"\", \"round_key\": \"9bcc539a366cf4b6b5b214265afe6aab\"}", "timestamp": "1746524486714526112", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.memory\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:41:26+0000\", \"exc_info\": null, \"filename\": \"memory.py\", \"funcName\": \"_filter_messages\", \"levelname\": \"DEBUG\", \"lineno\": 189, \"message\": \"remove 0 messages by filter\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\", \"before\": [\"id=None round_key='4ea52d44f88a5a05cf451fe648a0190e' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=' Hey you' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:08:35.440006+00:00'\", \"id=None round_key='4ea52d44f88a5a05cf451fe648a0190e' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content='Hey! How can I help you today?\\\\n' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:08:36.063773+00:00'\", \"id=None round_key='c6bbf68f7e4f7becff5a3cca85ad0fb2' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content='Hey you Hey you' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:09:16.220464+00:00'\", \"id=None round_key='c6bbf68f7e4f7becff5a3cca85ad0fb2' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content='Hey! How can I help you today?\\\\n' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:09:16.989566+00:00'\", \"id=None round_key='e5e4e20cfc6f096accbcb7ac11e607cd' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content='Even tell me a story' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:09:44.493662+00:00'\", \"id=None round_key='e5e4e20cfc6f096accbcb7ac11e607cd' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content=\\\"In a quiet village nestled beside a whispering forest, lived a young woman named Elara. She possessed a spirit as bright as the summer sun, but her days were filled with the humdrum of daily chores. One day, a traveling merchant arrived, his cart laden with exotic trinkets and tales of faraway lands. He told Elara of a hidden grove where the trees sang ancient melodies and the flowers bloomed in colors unseen by human eyes.\\\\n\\\\nIntrigued, Elara ventured into the forest, following a faint, shimmering path. Deeper and deeper she went, until she stumbled upon the grove. And there it was, just as the merchant had described \\u2013 trees humming with ethereal tunes, flowers glowing with vibrant hues. Elara spent hours in the grove, her heart filled with wonder. When she finally returned to her village, she brought with her not trinkets or treasures, but a newfound appreciation for the magic hidden in the ordinary. And so, Elara's days were no longer humdrum, for she knew that even in the quietest of villages, there was always a song to be heard, a color to be seen, a story to be lived.\\\\n\\\" tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:09:46.887510+00:00'\", \"id=None round_key='d6ba56447393b06de1aee49dd0e1f60f' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=\\\"Hey you what's the weather like\\\" tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:14:59.568249+00:00'\", \"id=None round_key='d6ba56447393b06de1aee49dd0e1f60f' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content='The weather in Shenzhen is light rain with a temperature of 31.71\\u00b0C. The humidity is 72%, and the wind is 4.63 m/s.\\\\n' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:15:01.091022+00:00'\", \"id=None round_key='feab9dd55a93163151c6154a3c980a8b' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=\\\"Hey you tell me what's going on\\\" tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:15:37.702547+00:00'\", \"id=None round_key='feab9dd55a93163151c6154a3c980a8b' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content=\\\"I can search the web for current events if you'd like. What kind of news are you interested in?\\\\n\\\" tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:15:38.877401+00:00'\", \"id=None round_key='60b77f5fc9405fbf75541d8c10e85127' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=' Who is Donald Trump' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:16:12.883037+00:00'\", \"id=None round_key='60b77f5fc9405fbf75541d8c10e85127' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content='Donald John Trump is an American businessman, politician, and media personality who served as the 45th president of the United States from 2017 to 2021.\\\\n' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:16:13.637240+00:00'\", \"id=None round_key='9bfa93900072af43cedb7e48f4953911' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content='Hey you tell me a story Hey tell me a story' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T05:20:14.858121+00:00'\", \"id=None round_key='9bcc539a366cf4b6b5b214265afe6aab' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content='Hey even who are you' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T09:41:26.572967+00:00'\"], \"after\": [], \"messages\": [], \"remove_round_keys\": \"\"}", "timestamp": "1746524486714497249", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.memory\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:41:26+0000\", \"exc_info\": null, \"filename\": \"memory.py\", \"funcName\": \"get\", \"levelname\": \"DEBUG\", \"lineno\": 67, \"message\": \"Loaded history 0 messages\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\", \"before\": [\"id=None round_key='4ea52d44f88a5a05cf451fe648a0190e' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=' Hey you' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:08:35.440006+00:00'\", \"id=None round_key='4ea52d44f88a5a05cf451fe648a0190e' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content='Hey! How can I help you today?\\\\n' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:08:36.063773+00:00'\", \"id=None round_key='c6bbf68f7e4f7becff5a3cca85ad0fb2' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content='Hey you Hey you' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:09:16.220464+00:00'\", \"id=None round_key='c6bbf68f7e4f7becff5a3cca85ad0fb2' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content='Hey! How can I help you today?\\\\n' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:09:16.989566+00:00'\", \"id=None round_key='e5e4e20cfc6f096accbcb7ac11e607cd' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content='Even tell me a story' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:09:44.493662+00:00'\", \"id=None round_key='e5e4e20cfc6f096accbcb7ac11e607cd' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content=\\\"In a quiet village nestled beside a whispering forest, lived a young woman named Elara. She possessed a spirit as bright as the summer sun, but her days were filled with the humdrum of daily chores. One day, a traveling merchant arrived, his cart laden with exotic trinkets and tales of faraway lands. He told Elara of a hidden grove where the trees sang ancient melodies and the flowers bloomed in colors unseen by human eyes.\\\\n\\\\nIntrigued, Elara ventured into the forest, following a faint, shimmering path. Deeper and deeper she went, until she stumbled upon the grove. And there it was, just as the merchant had described \\u2013 trees humming with ethereal tunes, flowers glowing with vibrant hues. Elara spent hours in the grove, her heart filled with wonder. When she finally returned to her village, she brought with her not trinkets or treasures, but a newfound appreciation for the magic hidden in the ordinary. And so, Elara's days were no longer humdrum, for she knew that even in the quietest of villages, there was always a song to be heard, a color to be seen, a story to be lived.\\\\n\\\" tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:09:46.887510+00:00'\", \"id=None round_key='d6ba56447393b06de1aee49dd0e1f60f' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=\\\"Hey you what's the weather like\\\" tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:14:59.568249+00:00'\", \"id=None round_key='d6ba56447393b06de1aee49dd0e1f60f' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content='The weather in Shenzhen is light rain with a temperature of 31.71\\u00b0C. The humidity is 72%, and the wind is 4.63 m/s.\\\\n' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:15:01.091022+00:00'\", \"id=None round_key='feab9dd55a93163151c6154a3c980a8b' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=\\\"Hey you tell me what's going on\\\" tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:15:37.702547+00:00'\", \"id=None round_key='feab9dd55a93163151c6154a3c980a8b' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content=\\\"I can search the web for current events if you'd like. What kind of news are you interested in?\\\\n\\\" tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:15:38.877401+00:00'\", \"id=None round_key='60b77f5fc9405fbf75541d8c10e85127' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=' Who is Donald Trump' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:16:12.883037+00:00'\", \"id=None round_key='60b77f5fc9405fbf75541d8c10e85127' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content='Donald John Trump is an American businessman, politician, and media personality who served as the 45th president of the United States from 2017 to 2021.\\\\n' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:16:13.637240+00:00'\", \"id=None round_key='9bfa93900072af43cedb7e48f4953911' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content='Hey you tell me a story Hey tell me a story' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T05:20:14.858121+00:00'\", \"id=None round_key='9bcc539a366cf4b6b5b214265afe6aab' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content='Hey even who are you' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T09:41:26.572967+00:00'\"], \"after\": [], \"messages\": []}", "timestamp": "1746524486714336443", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.memory\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:41:26+0000\", \"exc_info\": null, \"filename\": \"memory.py\", \"funcName\": \"get\", \"levelname\": \"WARNING\", \"lineno\": 58, \"message\": \"History message truncated, rewriting cache\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\", \"before\": [\"id=None round_key='4ea52d44f88a5a05cf451fe648a0190e' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=' Hey you' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:08:35.440006+00:00'\", \"id=None round_key='4ea52d44f88a5a05cf451fe648a0190e' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content='Hey! How can I help you today?\\\\n' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:08:36.063773+00:00'\", \"id=None round_key='c6bbf68f7e4f7becff5a3cca85ad0fb2' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content='Hey you Hey you' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:09:16.220464+00:00'\", \"id=None round_key='c6bbf68f7e4f7becff5a3cca85ad0fb2' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content='Hey! How can I help you today?\\\\n' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:09:16.989566+00:00'\", \"id=None round_key='e5e4e20cfc6f096accbcb7ac11e607cd' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content='Even tell me a story' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:09:44.493662+00:00'\", \"id=None round_key='e5e4e20cfc6f096accbcb7ac11e607cd' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content=\\\"In a quiet village nestled beside a whispering forest, lived a young woman named Elara. She possessed a spirit as bright as the summer sun, but her days were filled with the humdrum of daily chores. One day, a traveling merchant arrived, his cart laden with exotic trinkets and tales of faraway lands. He told Elara of a hidden grove where the trees sang ancient melodies and the flowers bloomed in colors unseen by human eyes.\\\\n\\\\nIntrigued, Elara ventured into the forest, following a faint, shimmering path. Deeper and deeper she went, until she stumbled upon the grove. And there it was, just as the merchant had described \\u2013 trees humming with ethereal tunes, flowers glowing with vibrant hues. Elara spent hours in the grove, her heart filled with wonder. When she finally returned to her village, she brought with her not trinkets or treasures, but a newfound appreciation for the magic hidden in the ordinary. And so, Elara's days were no longer humdrum, for she knew that even in the quietest of villages, there was always a song to be heard, a color to be seen, a story to be lived.\\\\n\\\" tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:09:46.887510+00:00'\", \"id=None round_key='d6ba56447393b06de1aee49dd0e1f60f' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=\\\"Hey you what's the weather like\\\" tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:14:59.568249+00:00'\", \"id=None round_key='d6ba56447393b06de1aee49dd0e1f60f' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content='The weather in Shenzhen is light rain with a temperature of 31.71\\u00b0C. The humidity is 72%, and the wind is 4.63 m/s.\\\\n' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:15:01.091022+00:00'\", \"id=None round_key='feab9dd55a93163151c6154a3c980a8b' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=\\\"Hey you tell me what's going on\\\" tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:15:37.702547+00:00'\", \"id=None round_key='feab9dd55a93163151c6154a3c980a8b' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content=\\\"I can search the web for current events if you'd like. What kind of news are you interested in?\\\\n\\\" tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:15:38.877401+00:00'\", \"id=None round_key='60b77f5fc9405fbf75541d8c10e85127' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content=' Who is Donald Trump' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:16:12.883037+00:00'\", \"id=None round_key='60b77f5fc9405fbf75541d8c10e85127' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.Assistant: 'assistant'> message_category=<MessageCategory.Text: 'text'> content='Donald John Trump is an American businessman, politician, and media personality who served as the 45th president of the United States from 2017 to 2021.\\\\n' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T04:16:13.637240+00:00'\", \"id=None round_key='9bfa93900072af43cedb7e48f4953911' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content='Hey you tell me a story Hey tell me a story' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T05:20:14.858121+00:00'\", \"id=None round_key='9bcc539a366cf4b6b5b214265afe6aab' session_id='01965c3c-97b3-7d11-8ae0-fa33bd790759' user_id='19281' role=<MessageRole.User: 'user'> message_category=<MessageCategory.Text: 'text'> content='Hey even who are you' tool_call_id=None name=None tool_calls=None attrs=None created_at='2025-05-06T09:41:26.572967+00:00'\"], \"after\": []}", "timestamp": "1746524486714278691", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}, {"line": "{\"name\": \"jarvis.business.ai.memory\", \"process\": 165, \"thread\": 139347139855936, \"threadName\": \"Thread-1 (_run_user_code_event_loop)\", \"taskName\": null, \"asctime\": \"2025-05-06 09:41:26+0000\", \"exc_info\": null, \"filename\": \"memory.py\", \"funcName\": \"get\", \"levelname\": \"DEBUG\", \"lineno\": 48, \"message\": \"Getting history messages\", \"session_id\": \"01965c3c-97b3-7d11-8ae0-fa33bd790759\", \"user_id\": \"19281\"}", "timestamp": "1746524486714097639", "fields": {"app": "ai-jarvis", "detected_level": "info", "filename": "/ai-jarvis/logs/jarvis.log", "namespace": "test-ai", "service": "<PERSON>", "service_name": "<PERSON>"}}]