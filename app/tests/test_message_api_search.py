import unittest

from business.chat_session.store import Message, MessageRole, Session<PERSON>anager, MessageManager
from config.settings import settings
import datetime
from motor.motor_asyncio import AsyncIOMotorClient
import redis.asyncio as redis
import asyncio

from models.message import ChatMessageQuestionOrAnswerType, MessageCategory


class TestMessageAPIManager(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self):
        settings.MONGO_DB_NAME = "test_session"
        self._mgo = AsyncIOMotorClient(settings.MONGO_URI)
        self._redis = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.CACHE_MESSAGE_LIST_DB,
        )
        await self._mgo.drop_database(settings.MONGO_DB_NAME)
        self._client = MessageManager(self._mgo, self._redis)

        USER = "user01"
        SESS = "sess01"
        TEST_MESSAGES = [
            Message(
                session_id=SESS,
                user_id=USER,
                role=MessageRole.User,
                round_key="round_1",
                message_category=MessageCategory.Text,
                content=f"UserInput",
            ),
            Message(
                session_id=SESS,
                user_id=USER,
                role=MessageRole.Assistant,
                round_key="round_1",
                message_category=MessageCategory.Text,
                content=f"AssistantOutput",
            ),
            Message(
                session_id=SESS,
                user_id=USER,
                role=MessageRole.User,
                round_key="round_2",
                message_category=MessageCategory.Text,
                content=f"UserInput2",
            ),
            Message(
                session_id=SESS,
                user_id=USER,
                role=MessageRole.Assistant,
                round_key="round_2",
                message_category=MessageCategory.Intent,
                content='{"intent":"disp_bright_set","score":0.8713407516479492,"entities":{"value":100}}',
            ),
            Message(
                session_id="HAHA",
                user_id="HAHA",
                role=MessageRole.User,
                round_key="round_1",
                message_category=MessageCategory.Text,
                content=f"NOT_MATCH",
            ),
            Message(
                session_id="HAHA",
                user_id="HAHA",
                role=MessageRole.Assistant,
                round_key="round_1",
                message_category=MessageCategory.Text,
                content=f"NOT_MATCH",
            ),
            Message(
                session_id="sess_u02-s01",
                user_id="user_02",
                role=MessageRole.User,
                round_key="round_3",
                message_category=MessageCategory.Text,
                content="Hello",
            ),
            Message(
                session_id="sess_u02-s01",
                user_id="user_02",
                role=MessageRole.Assistant,
                round_key="round_3",
                message_category=MessageCategory.Text,
                content="World",
            ),
            Message(
                session_id="sess_u02-s01",
                user_id="user_02",
                role=MessageRole.User,
                round_key="round_4",
                message_category=MessageCategory.Text,
                content="Hello2",
            ),
            Message(
                session_id="sess_u02-s01",
                user_id="user_02",
                role=MessageRole.Assistant,
                round_key="round_4",
                message_category=MessageCategory.Intent,
                content='{"intent":"disp_bright_set","score":0.8713407516479492,"entities":{"value":100}}',
            ),
        ]
        for msg in TEST_MESSAGES:
            await self._client.push_message(msg)
        await super().asyncSetUp()

    async def asyncTearDown(self):
        self._mgo.close()
        await self._redis.aclose()
        return await super().asyncTearDown()

    async def test_search_message(self):
        USER = "user01"
        SESS = "sess01"

        # 列出消息
        msg, cnt, _ = await self._client.api_search_messages(USER, acquire_count=True)
        self.assertEqual(len(msg), 2)
        self.assertEqual(cnt, 2)

        # 确认消息
        self.assertEqual(msg[0].session_id, SESS)
        self.assertEqual(msg[0].user_id, USER)
        self.assertEqual(msg[0].question.type, ChatMessageQuestionOrAnswerType.Text)
        self.assertEqual(msg[0].question.content, "UserInput")
        self.assertEqual(msg[0].answer.type, ChatMessageQuestionOrAnswerType.Text)
        self.assertEqual(msg[0].answer.content, "AssistantOutput")

        self.assertEqual(msg[1].session_id, SESS)
        self.assertEqual(msg[1].user_id, USER)
        self.assertEqual(msg[1].question.type, ChatMessageQuestionOrAnswerType.Text)
        self.assertEqual(msg[1].question.content, "UserInput2")
        self.assertEqual(msg[1].answer.type, ChatMessageQuestionOrAnswerType.Intent)
        self.assertEqual(msg[1].answer.intent_category, "disp_bright_set")
        self.assertEqual(msg[1].answer.intent_score, 0.8713407516479492)
        self.assertEqual(msg[1].answer.intent_entities, {"value": 100})

    async def test_search_message_with_session(self):
        USER = "user_02"
        SESS = "sess_u02-s01"

        msg, cnt, _ = await self._client.api_search_messages(USER, session_ids=[SESS], acquire_count=True)
        self.assertEqual(len(msg), 2)
        self.assertEqual(cnt, 2)
        self.assertEqual(msg[0].session_id, SESS)
        self.assertEqual(msg[0].user_id, USER)
        self.assertEqual(msg[0].question.type, ChatMessageQuestionOrAnswerType.Text)
        self.assertEqual(msg[0].question.content, "Hello")
        self.assertEqual(msg[0].answer.type, ChatMessageQuestionOrAnswerType.Text)
        self.assertEqual(msg[0].answer.content, "World")
        self.assertEqual(msg[0].answer.intent_category, None)
        self.assertEqual(msg[0].answer.intent_score, None)
        self.assertEqual(msg[0].answer.intent_entities, None)

    async def test_search_message_with_keyword_found_one(self):
        USER = "user_02"
        SESS = "sess_u02-s01"

        msg, cnt, _ = await self._client.api_search_messages(USER, message_like="Hello", acquire_count=True)
        self.assertEqual(len(msg), 2)
        self.assertEqual(cnt, 2)
        self.assertEqual(msg[0].session_id, SESS)
        self.assertEqual(msg[0].user_id, USER)
        self.assertEqual(msg[0].question.type, ChatMessageQuestionOrAnswerType.Text)
        self.assertEqual(msg[0].question.content, "Hello")
        self.assertEqual(msg[0].answer.type, ChatMessageQuestionOrAnswerType.Text)
        self.assertEqual(msg[0].answer.content, "World")
        self.assertEqual(msg[0].answer.intent_category, None)
        self.assertEqual(msg[0].answer.intent_score, None)
        self.assertEqual(msg[0].answer.intent_entities, None)

    async def test_search_message_with_keyword_found_multi(self):
        USER = "user_02"
        SESS = "sess_u02-s01"

        msg, cnt, _ = await self._client.api_search_messages(USER, message_like="Hello", acquire_count=True)
        self.assertEqual(len(msg), 2)
        self.assertEqual(cnt, 2)
        self.assertEqual(msg[0].session_id, SESS)
        self.assertEqual(msg[0].user_id, USER)
        self.assertEqual(msg[0].question.type, ChatMessageQuestionOrAnswerType.Text)
        self.assertEqual(msg[0].question.content, "Hello")
        self.assertEqual(msg[0].answer.type, ChatMessageQuestionOrAnswerType.Text)
        self.assertEqual(msg[0].answer.content, "World")
        self.assertEqual(msg[0].answer.intent_category, None)
        self.assertEqual(msg[0].answer.intent_score, None)
        self.assertEqual(msg[0].answer.intent_entities, None)

    async def test_search_message_with_sort(self):
        USER = "user01"
        SESS = "sess01"

        msg, cnt, _ = await self._client.api_search_messages(
            USER,
            session_ids=[SESS],
            sorts=[("created_at", -1)],
            acquire_count=True,
        )
        self.assertEqual(len(msg), 2)
        self.assertEqual(cnt, 2)
        self.assertEqual(msg[0].question.content, "UserInput2")

        msg, cnt, _ = await self._client.api_search_messages(
            USER,
            session_ids=[SESS],
            sorts=[("created_at", 1)],
            acquire_count=True,
        )
        self.assertEqual(len(msg), 2)
        self.assertEqual(cnt, 2)
        self.assertEqual(msg[0].question.content, "UserInput")

    async def test_search_message_with_page(self):
        USER = "user01"
        SESS = "sess01"

        msg1, cnt, pg = await self._client.api_search_messages(
            USER, session_ids=[SESS], page_size=1, acquire_count=True
        )
        self.assertEqual(len(msg1), 1)
        self.assertEqual(cnt, 2)
        self.assertEqual(msg1[0].question.content, "UserInput")

        msg2, cnt, _ = await self._client.api_search_messages(
            USER, session_ids=[SESS], page_token=pg, page_size=1, acquire_count=True
        )

        self.assertEqual(len(msg2), 1)
        self.assertEqual(cnt, 2)
        self.assertEqual(msg2[0].question.content, "UserInput2")

    async def test_search_message_with_page_token_and_sort(self):

        USER = "user01"
        SESS = "sess01"

        msg1, cnt, pg = await self._client.api_search_messages(
            USER,
            session_ids=[SESS],
            page_size=1,
            sorts=[("created_at", -1)],
            acquire_count=True,
        )
        self.assertEqual(len(msg1), 1)
        self.assertEqual(cnt, 2)
        self.assertEqual(msg1[0].question.content, "UserInput2")

        msg2, cnt, _ = await self._client.api_search_messages(
            USER,
            session_ids=[SESS],
            page_token=pg,
            sorts=[("created_at", -1)],
            page_size=1,
            acquire_count=True,
        )

        self.assertEqual(len(msg2), 1)
        self.assertEqual(cnt, 2)
        self.assertEqual(msg2[0].question.content, "UserInput")

    async def test_dump_message(self):
        USER = "user01"
        SESS = "sess01"
        msg, cnt, _ = await self._client.api_search_messages(USER, session_ids=[SESS], acquire_count=True)
        for i in range(len(msg)):
            print(msg[i].model_dump_json(indent=2))
