import unittest

from business.ai.memory import ChattingMemory
from business.chat_session.store import Message, Message<PERSON><PERSON>, Session<PERSON>anager, MessageManager
from config.settings import settings
import datetime
from motor.motor_asyncio import AsyncIOMotorClient
import redis.asyncio as redis
import asyncio

from models.message import MessageCategory


class TestMemoryManager(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self):
        await super().asyncSetUp()
        settings.MONGO_DB_NAME = "test_session"
        self._mgo = AsyncIOMotorClient(settings.MONGO_URI)
        self._redis = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.CACHE_MESSAGE_LIST_DB,
        )
        await self._mgo.drop_database(settings.MONGO_DB_NAME)
        self._client = MessageManager(self._mgo, self._redis)

    async def asyncTearDown(self):
        self._mgo.close()
        await self._redis.aclose()
        return await super().asyncTearDown()

    async def test_get_history_msgs_ignore_intent(self):
        USER = "user_01"
        SESS = "sess_01"

        rule = ChattingMemory.Rule()
        rule.use_cache = False
        memory = ChattingMemory(USER, SESS, rule, self._client, SessionManager(self._mgo), self._redis)
        await memory.init()

        await memory.add(
            Message(
                round_key="r1",
                session_id=SESS,
                user_id=USER,
                role=MessageRole.User,
                message_category=MessageCategory.Text,
                content="Input",
            )
        )

        await memory.add(
            Message(
                round_key="r1",
                session_id=SESS,
                user_id=USER,
                role=MessageRole.Assistant,
                message_category=MessageCategory.Text,
                content="Output",
            )
        )

        await memory.add(
            Message(
                round_key="r2",
                session_id=SESS,
                user_id=USER,
                role=MessageRole.User,
                message_category=MessageCategory.Text,
                content="IntentInput",
            )
        )

        await memory.add(
            Message(
                round_key="r2",
                session_id=SESS,
                user_id=USER,
                role=MessageRole.User,
                message_category=MessageCategory.Intent,
                content="IntentOutput",
            )
        )

        history = await memory.get()
        self.assertEqual(len(history), 2)

        self.assertEqual(history[1].content, "Input")
        self.assertEqual(history[0].content, "Output")

    async def test_get_history_msg_truncated_by_time(self):
        USER = "user_01"
        SESS = "sess_01"

        rule = ChattingMemory.Rule()
        rule.use_cache = False
        rule.max_ttl = datetime.timedelta(seconds=3)
        memory = ChattingMemory(USER, SESS, rule, self._client, SessionManager(self._mgo), self._redis)
        await memory.init()

        now = datetime.datetime.now().astimezone(datetime.timezone.utc)
        await memory.add(
            Message(
                round_key="r1",
                session_id=SESS,
                user_id=USER,
                role=MessageRole.User,
                message_category=MessageCategory.Text,
                content="Input1",
                created_at=(now - datetime.timedelta(seconds=5)).isoformat(),
            )
        )

        await memory.add(
            Message(
                round_key="r1",
                session_id=SESS,
                user_id=USER,
                role=MessageRole.Assistant,
                message_category=MessageCategory.Text,
                content="Output1",
                created_at=(now - datetime.timedelta(seconds=5)).isoformat(),
            )
        )

        await memory.add(
            Message(
                round_key="r2",
                session_id=SESS,
                user_id=USER,
                role=MessageRole.User,
                message_category=MessageCategory.Text,
                content="Input2",
                created_at=(now - datetime.timedelta(seconds=1, microseconds=100)).isoformat(),
            )
        )

        await memory.add(
            Message(
                round_key="r2",
                session_id=SESS,
                user_id=USER,
                role=MessageRole.Assistant,
                message_category=MessageCategory.Text,
                content="Output2",
                created_at=(now - datetime.timedelta(seconds=1, microseconds=50)).isoformat(),
            )
        )

        await memory.add(
            Message(
                round_key="r3",
                session_id=SESS,
                user_id=USER,
                role=MessageRole.User,
                message_category=MessageCategory.Text,
                content="Input3",
            )
        )

        await memory.add(
            Message(
                round_key="r3",
                session_id=SESS,
                user_id=USER,
                role=MessageRole.Assistant,
                message_category=MessageCategory.Text,
                content="Output3",
            )
        )

        history = await memory.get()
        self.assertEqual(len(history), 4)
        self.assertEqual(history[0].content, "Output3")
        self.assertEqual(history[1].content, "Input3")
        self.assertEqual(history[2].content, "Output2")
        self.assertEqual(history[3].content, "Input2")
