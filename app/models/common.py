# coding = utf-8
import enum
from typing import Optional, Union, Dict, Any
from bson import ObjectId
from pydantic import BaseModel, ConfigDict, Field, computed_field
from utils.datetime import get_local_time_str
from pydantic_core import core_schema


class SexCategory(int, enum.Enum):
    Unknown = 0
    Male = 1
    Female = 2


class ResponseLengthCategory(str, enum.Enum):
    Compact = "evenAiShort"
    Normal = "evenAiMiddle"
    Extensive = "evenAi"


class JarvisBaseModel(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True, populate_by_name=True)


class Location(BaseModel):
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    country: Optional[str] = Field(None, example="US")
    state: Optional[str] = Field(None, example="Texas")
    city: Optional[str] = Field(None, example="Austin")
    county: Optional[str] = Field(None, example="Travis County")
    street: Optional[str] = Field(None, example="123 Main St")


class Context(BaseModel):
    session_id: str
    location: Optional[Location] = Field(default_factory=Location)
    timestamp: Optional[str] = Field(None, example="2025-02-10T01:39:39.756997Z", description="原始时间戳（任意格式）")
    timezone: Optional[str] = Field(None, example="UTC+8", description="时区信息，例如：UTC+8 或 Asia/Shanghai")
    language: Optional[str] = Field("", example="en", description="语言信息，例如：en 或 zh")
    sex: Optional[SexCategory] = Field(SexCategory.Unknown, description="性别")
    response_length: Optional[ResponseLengthCategory] = Field(ResponseLengthCategory.Compact, description="响应长度")
    user_id: str = ""

    @classmethod
    def from_user_input(cls, user_input: Union[dict, Any], session_id: str):
        """从user_input字典或对象创建Context"""
        if isinstance(user_input, dict):
            location_data = user_input.get("location", {})
            return cls(
                location=Location(**location_data),
                session_id=session_id,
                timestamp=user_input.get("timestamp"),
                timezone=user_input.get("timezone"),
                language=user_input.get("language"),
                sex=user_input.get("sex"),
                response_length=user_input.get("response_length"),
                user_id=user_input.get("user_id"),
            )
        else:  # 处理UserInput对象
            return cls(
                location=user_input.location or Location(),
                session_id=session_id,
                timestamp=user_input.timestamp,
                timezone=user_input.timezone,
                language=user_input.language,
                sex=user_input.sex,
                response_length=user_input.response_length,
                user_id=user_input.user_id,
            )

    @computed_field
    @property
    def local_time(self) -> str:
        return get_local_time_str(self.timestamp, self.timezone)


class ResponseType(enum.Enum):
    Cmd = "cmd"
    Text = "text"
    Start = "start"
    End = "end"


class ToolResult(BaseModel):
    result: Optional[Union[str, Dict]]
    result_type: ResponseType = ResponseType.Text  # 结果类型 (暂时没有想到很好的方式，先这样)
    direct_output: bool = False  # 是否直接输出结果


class UserInput(BaseModel):
    location: Optional[Location] = Field(default_factory=Location)
    timestamp: Optional[str] = Field(None, example="2025-02-10T01:39:39.756997Z", description="原始时间戳（任意格式）")
    timezone: Optional[str] = Field(None, example="UTC+8", description="时区信息，例如：UTC+8 或 Asia/Shanghai")
    language: Optional[str] = Field(None, example="en", description="语言信息，例如：en 或 zh")
    sex: Optional[SexCategory] = Field(SexCategory.Unknown, description="性别")
    response_length: Optional[ResponseLengthCategory] = Field(ResponseLengthCategory.Compact, description="响应长度")
    user_id: Optional[str] = Field("", description="用户ID")


class AITextRequest(BaseModel):
    text: str = Field(..., min_length=1, example="今天天气怎么样？")
    user_input: Optional[UserInput] = Field(default_factory=UserInput)


class PyObjectId(str):
    @classmethod
    def __get_pydantic_core_schema__(cls, _source_type: Any, _handler: Any) -> core_schema.CoreSchema:
        return core_schema.json_or_python_schema(
            json_schema=core_schema.str_schema(),
            python_schema=core_schema.union_schema(
                [
                    core_schema.is_instance_schema(ObjectId),
                    core_schema.chain_schema(
                        [
                            core_schema.str_schema(),
                            core_schema.no_info_plain_validator_function(cls.validate),
                        ]
                    ),
                ]
            ),
            serialization=core_schema.plain_serializer_function_ser_schema(lambda x: str(x)),
        )

    @classmethod
    def validate(cls, value) -> ObjectId:
        if not ObjectId.is_valid(value):
            raise ValueError("Invalid ObjectId")

        return ObjectId(value)
