import datetime
import enum
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field
from models.common import JarvisBaseModel, PyObjectId


class MessageRole(enum.Enum):
    """
    消息角色
    """

    System = "system"
    User = "user"
    Assistant = "assistant"
    Tools = "tool"
    # 非对外消息
    CustomIntent = "custom_intent"  # 意图结果

    @classmethod
    def display_roles(cls, raw=False):
        return [x.value if raw else x for x in [cls.User, cls.Assistant, cls.Tools]]


class MessageToolCallResult(BaseModel):
    type: str = "text"
    result: Any = None  # -> 函数参数


class MessageToolCallFunction(BaseModel):
    name: str = ""  # -> 函数名
    args: dict = {}  # -> 函数参数
    result: Optional[MessageToolCallResult] = None


class MessageToolCall(BaseModel):
    id: Optional[str] = None
    type: str = "function"
    function: MessageToolCallFunction = {}


class MessageAttr(enum.Enum):
    Sentiment = "sentiment"  # 情感类型


class MessageAttrSentimentType(enum.Enum):
    Like = "like"
    Dislike = "dislike"


class MessageCategory(enum.Enum):
    Text = "text"  # 文本消息
    Intent = "intent"  # 意图消息


class Message(JarvisBaseModel):
    """
    后续大葱基于各个模型的数据结构继续抽象吧
    """

    id: Optional[PyObjectId] = Field(None, alias="_id")
    round_key: Optional[str] = None  # 用于绑定一系列 Message 为一轮对话的 key

    session_id: str = ""
    user_id: str = ""
    role: MessageRole
    message_category: MessageCategory = MessageCategory.Text  # 消息类型，会影响值有效性以及解析方式
    # Text/Intent
    content: Optional[str] = None

    # 工具相关
    tool_call_id: Optional[str] = None
    name: Optional[str] = None
    tool_calls: Optional[List[MessageToolCall]] = None

    # 扩展属性
    attrs: Optional[Dict[MessageAttr, str]] = None

    # 表内属性
    created_at: str = Field(
        default_factory=lambda: datetime.datetime.now().astimezone(datetime.timezone.utc).isoformat()
    )


class ChatMessageQuestionOrAnswerType(enum.Enum):
    Text = "text"
    Intent = "intent"


class APIChatMessageQuestionOrAnswer(JarvisBaseModel):
    id: PyObjectId
    type: ChatMessageQuestionOrAnswerType = ChatMessageQuestionOrAnswerType.Text
    # 文本区域数据
    content: str = ""
    # 意图数据区域
    intent_category: Optional[str] = None
    intent_entities: Optional[Dict[str, Any]] = None
    intent_score: Optional[float] = None

    created_at: str


class APIChatMessageEntity(JarvisBaseModel):
    """
    该结构用于定义返回的 Message 结构，因为 DB 结构不是人看的
    """

    session_id: str = ""
    user_id: str = ""

    question: APIChatMessageQuestionOrAnswer
    answer: APIChatMessageQuestionOrAnswer

    # 消息属性
    attrs: Optional[Dict[str, Any]] = {}
