import enum
from bson import ObjectId
from pydantic import Field, BaseModel
from typing import List, Generic, Optional, TypeVar
from models.common import JarvisBaseModel, PyObjectId
from models.message import MessageAttr


class OrderByArgument(BaseModel):
    field: str
    order: int = Field(default=1, description="1: 升序, -1: 降序")


class FilterOperation(enum.Enum):
    Like = "like"
    Equal = "equal"


class FilterArgument(BaseModel):
    field: str = ""
    operation: FilterOperation = FilterOperation.Equal
    value: str = ""


class PagingArguments(BaseModel):
    page_token: Optional[str] = None
    page_size: int = 20
    sort: List[OrderByArgument] = []
    filters: List[FilterArgument] = []
    acquire_count: bool = Field(
        default=False, description="是否获取总数, 不推荐开启，交互做无限加载吧，Mongo 在这种场景下性能不好"
    )


class ManagerSessionPagingArguments(PagingArguments):
    user_id: str


class ManagerSessionDeleteArguments(BaseModel):
    user_id: str
    session_id: Optional[List[str]] = []


class ManagerSessionTruncateMessageArguments(BaseModel):
    user_id: str
    session_id: str
    max_size: int


class ManagerMessageListUserAllArguments(PagingArguments):
    user_id: str
    prev_message: Optional[int] = 0


class ManagerMessageSearchArguments(ManagerMessageListUserAllArguments):
    session_ids: Optional[List[str]] = None


class ManagerMessageSentimentOperationArguments(BaseModel):
    id: PyObjectId
    user_id: str
    like: Optional[bool]


class ManagerMessageDeleteArguments(BaseModel):
    user_id: str
    message_ids: Optional[List[PyObjectId]] = []


class APIErrorCode(enum.Enum):
    OK = 0
    InternalError = 1
    NotFound = 2
    InvalidArgument = 3


# 定义一个泛型变量 T
T = TypeVar("T")


class APIResponse(JarvisBaseModel, Generic[T]):
    code: APIErrorCode = APIErrorCode.OK
    msg: str = ""
    data: Optional[T] = None


class PagingResponse(JarvisBaseModel, Generic[T]):
    page_token: Optional[PyObjectId] = None
    total: Optional[int] = None
    result: Optional[T] = None
