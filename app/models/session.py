from pydantic import BaseModel, Field
from models.common import JarvisBaseModel, PyObjectId
from typing import Any, List, Optional


class SessionTitleExtra(BaseModel):
    entity: Optional[str] = None
    operator: Optional[str] = None
    values: Optional[Any] = None


class SessionInfo(JarvisBaseModel):
    """
    定义基础的 Session 信息
    """

    id: Optional[PyObjectId] = Field(None, alias="_id")

    session_id: str
    user_id: str
    title: Optional[str]
    created_at: str
    updated_at: str

    title_extra: Optional[SessionTitleExtra] = Field(None, description="会话标题的额外信息")

    # 其他元数据想到再说
