import enum


class OperationCategory(str, enum.Enum):
    AutoOff = "auto_off"
    AutoOn = "auto_on"
    On = "on"
    Off = "off"
    Decr = "dec"
    Incr = "inc"
    SetToMax = "max"
    SetToMin = "min"
    SetTo = "set"


class EntityCategory(str, enum.Enum):
    DisplayBrightness = "disp_bright"  # 屏幕显示亮度
    DisplayDistance = "disp_dist"  #  屏幕显示距离
    DisplayHeight = "disp_ht"  # 屏幕显示高度
    Display = "disp"    # 显示
    DP = "dp"  # Direct Push
    Navi = "navi"  # 导航
    NoOp = "noop"  # 无操作
    Notify = "notify"  # 通知
    QuickNote = "qn"  # 快速笔记
    Slient = "silent"  # 静音
    Teleprompt = "telep"  # 提词器
    Transcribe = "transc"  # 转录
    Translate = "transl"  # 翻译


EntitySupportedCategory = {
    EntityCategory.DisplayBrightness: [
        OperationCategory.AutoOff,
        OperationCategory.AutoOn,
        OperationCategory.Decr,
        OperationCategory.Incr,
        OperationCategory.SetToMax,
        OperationCategory.SetToMin,
        OperationCategory.SetTo,
    ],
    EntityCategory.DisplayDistance: [
        OperationCategory.Decr,
        OperationCategory.Incr,
        OperationCategory.SetToMax,
        OperationCategory.SetToMin,
        OperationCategory.SetTo,
    ],
    EntityCategory.DisplayHeight: [
        OperationCategory.Decr,
        OperationCategory.Incr,
        OperationCategory.SetToMax,
        OperationCategory.SetToMin,
        OperationCategory.SetTo,
    ],
    EntityCategory.Display: [
        OperationCategory.On,
    ],
    EntityCategory.DP: [
        OperationCategory.On,
        OperationCategory.Off,
    ],
    EntityCategory.Notify: [
        OperationCategory.On,
        OperationCategory.Off,
    ],
    EntityCategory.QuickNote: [
        OperationCategory.On,
    ],
    EntityCategory.Slient: [
        OperationCategory.On,
    ],
    EntityCategory.Teleprompt: [
        OperationCategory.On,
    ],
    EntityCategory.Transcribe: [
        OperationCategory.On,
    ],
    EntityCategory.Translate: [
        OperationCategory.On,
    ],
    EntityCategory.Navi: [
        OperationCategory.On,
    ],
}


class IntentCategory(str, enum.Enum):
    # 动态生成 IntentCategory 的枚举值
    def _generate_intent_categories():
        categories = {}
        category_mappings = {}
        for entity_name, entity_value in EntityCategory.__members__.items():
            if entity_value not in EntitySupportedCategory.keys():
                categories[entity_value.value] = entity_value.value
            else:
                for operator in EntitySupportedCategory[entity_value]:
                    intent_name = f"{entity_value.value}_{operator.value}"
                    intent_key = f"{entity_name}_{operator.name}"
                    categories[intent_key] = intent_name
                    category_mappings[intent_key] = (entity_value, operator)
        return categories, category_mappings

    # 将动态生成的枚举值添加到类中
    locals().update(_generate_intent_categories()[0])

    @classmethod
    def values(cls):
        return cls.__members__.values()

    @classmethod
    def get_entity_and_operator(cls, key):
        mapping = cls.get_mappings()
        return mapping.get(IntentCategory(key).name, (None, None))

    @classmethod
    def get_mappings(cls):
        if not hasattr(cls, "_MAPPING"):
            cls._MAPPING = cls._generate_intent_categories()[1]
        return cls._MAPPING
