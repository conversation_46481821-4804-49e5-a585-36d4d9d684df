from fastapi import APIRouter, Response, Depends
from prometheus_client import CONTENT_TYPE_LATEST, REGISTRY, generate_latest

import dependencies
from config.settings import RunMode, settings
from utils import logging

router = APIRouter()
LOG = logging.getLogger(__name__)


@router.get("/metrics")
async def metrics():
    """
    RAY 模式下指标暴露到了ray自己的metrics中
    """
    return Response(generate_latest(REGISTRY), headers={"Content-Type": CONTENT_TYPE_LATEST})
