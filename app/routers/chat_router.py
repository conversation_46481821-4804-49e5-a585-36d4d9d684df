import json

from fastapi import APIRouter, WebSocket, Depends
from starlette.responses import JSONResponse

from business.ai.ai_agent import AIAgentResponseChunk, AIAgentService
from business.chat_session.title_gen import update_session_title
from business.clients.chat_session import get_shared_message_manager, get_shared_session_manager
from business.transcribe.transcribe import TranscribeService
from models.common import Context, AITextRequest, ResponseType
from typing import AsyncGenerator, Optional, Union
from pydantic import ValidationError, BaseModel
import ujson
import dependencies
from fastapi import HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from datetime import datetime
from business.chat_session.store import (
    SessionManager,
    MessageManager,
)
from utils import logging

router = APIRouter()
LOG = logging.getLogger(__name__)


class SSEMessage(BaseModel):
    """
    参考 https://developer.mozilla.org/zh-CN/docs/Web/API/Server-sent_events/Using_server-sent_events
    往客户端的 SSE 消息先封装到规范格式，便于通用裤解析
    """

    event: ResponseType
    id: int
    data: Optional[Union[str | dict]] = None


async def agent_chat(
    ctx: Context, input: str, ai_service: AIAgentService
) -> AsyncGenerator[AIAgentResponseChunk, None]:
    yield AIAgentResponseChunk(type=ResponseType.Start)
    async for chunk in ai_service.process_stream(ctx, input):
        yield chunk
    yield AIAgentResponseChunk(type=ResponseType.End)


def sse_response(resp: AIAgentResponseChunk):

    try:
        # return SSEMessage(
        #     event=resp.type,
        #     data=resp.content,
        #     id=int(datetime.now(timezone.utc).timestamp() * 1000),
        # ).model_dump_json()
        # todo: 暂时先返回原始数据
        return resp.model_dump_json()
    except Exception as e:
        LOG.error("Failed to serialize SSE message", extra={"error": str(e), "resp": resp})
        return None


async def gen_session_title_summary(
    session_id: str, ctx: Context, ai_agent: AIAgentService, session_mgr: SessionManager, message_mgr: MessageManager
):
    """
    用户会话标题
    """
    sess = await session_mgr.must_get_session(session_id, ctx.user_id)
    if not sess:
        LOG.error(f"[{session_id}] Session not found")
        return
    '''
    if sess.title:
        # 已经计算过标题的会话不再计算
        # LOG.info(f"[{session_id}] gen_session_title_summary: title exists {sess.title}")
        return

    await update_session_title(ctx, sess, session_mgr, message_mgr)
    '''

# 目前路由没有层级。。直接写吧
@router.websocket("/ws/streaming/ai/text/{session_id}")
async def chat_by_text_from_websocket(
    websocket: WebSocket,
    session_id: str,
    taskpool: BackgroundTasks,
    ai_agent: AIAgentService = Depends(dependencies.get_ai_service_client),
    session_mgr: SessionManager = Depends(get_shared_session_manager),
    message_mgr: MessageManager = Depends(get_shared_message_manager),
):
    """使用websocket的方式，接收文本，并返回AI流式响应"""
    await websocket.accept()
    ctx = None
    try:
        while True:  # 保持连接持续监听
            # 接收并验证客户端发送的数据
            input_chunk = await websocket.receive_text()

            # 使用Pydantic模型进行反序列化
            try:
                request_body = AITextRequest.model_validate_json(input_chunk)
            except ValidationError as e:
                await websocket.send_text(
                    ujson.dumps({"type": "text", "content": "Invalid request format", "errors": e.errors()})
                )
                continue

            # 使用验证后的user_input
            ctx = Context.from_user_input(user_input=request_body.user_input, session_id=session_id)

            async for chunk in agent_chat(ctx, request_body.text, ai_agent):
                await websocket.send_text(chunk.model_dump_json())

    except Exception as e:
        LOG.error(f"[{session_id}] Client error: {str(e)}", exc_info=True)
    finally:
        await websocket.close()
        if ctx:
            taskpool.add_task(
                gen_session_title_summary,
                session_id,
                ctx,
                ai_agent,
                session_mgr,
                message_mgr,
            )


@router.websocket("/ws/streaming/ai/audio/{session_id}")
async def ws_ai_by_audio(
    self,
    websocket: WebSocket,
    session_id: str,
    taskpool: BackgroundTasks,
    ai_service: AIAgentService = Depends(dependencies.get_ai_service_client),
    # transc_serivce: TranscribeService = Depends(dependencies.get_transcriber_client),
    session_mgr: SessionManager = Depends(get_shared_session_manager),
    message_mgr: MessageManager = Depends(get_shared_message_manager),
):
    """使用websocket的方式，接收音频分片，并返回AI流式响应"""
    await websocket.accept()
    ctx = Context(session_id=session_id)
    try:
        while True:  # 保持连接持续监听
            # 接收客户端发送的音频分片
            # audio_chunk = await websocket.receive_bytes()

            # 语音转文字
            # text = await transc_serivce.transcirbe_audio_stream(ctx, audio_chunk)
            text = "test"
            if text:
                await websocket.send_text(
                    ujson.dumps(
                        {
                            "type": "transcribe",
                            "text": text,
                        }
                    )
                )

                await websocket.send_text(ujson.dumps({"type": "start"}))

                # 流式处理AI响应
                async for chunk in ai_service.process_stream(ctx, text):
                    await websocket.send_text(chunk)

                await websocket.send_text(ujson.dumps({"type": "end"}))

    except Exception as e:
        LOG.error(f"[{session_id}] Client error: {str(e)}", exc_info=True)
    finally:
        await websocket.close()
        taskpool.add_task(
            gen_session_title_summary,
            session_id,
            ctx,
            ai_service,
            session_mgr,
            message_mgr,
        )


@router.post("/streaming/ai/text/{session_id}")
async def stream_ai_by_text(
    session_id: str,
    request_body: AITextRequest,
    taskpool: BackgroundTasks,
    ai_agent: AIAgentService = Depends(dependencies.get_ai_service_client),
    session_mgr: SessionManager = Depends(get_shared_session_manager),
    message_mgr: MessageManager = Depends(get_shared_message_manager),
):
    """HTTP流式响应接口，接收文本并返回AI流式响应"""
    ctx = None
    try:
        # 先初始化context
        ctx = Context.from_user_input(user_input=request_body.user_input, session_id=session_id)
        # 获取文本数据
        text = request_body.text

        # 创建异步生成器流
        async def response_generator():
            async for chunk in agent_chat(ctx, text, ai_agent):
                msg = sse_response(chunk)
                if msg:
                    yield msg
                    yield "\n"

        # 使用Ray Serve优化的流式响应
        return StreamingResponse(
            response_generator(),
            media_type="text/event-stream",
            headers={
                "X-Session-ID": session_id,  # TODO: 重新定义头
                "Cache-Control": "no-cache",  # 防止缓存
                "Connection": "keep-alive",  # 保持长连接
            },
        )
    except ujson.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format")
    except Exception as e:
        LOG.error("Failed to process request", extra={"error": str(e), "session": session_id}, exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": str(e), "session_id": session_id, "timestamp": datetime.now().isoformat()},
        )
    finally:
        taskpool.add_task(
            gen_session_title_summary,
            session_id,
            ctx,
            ai_agent,
            session_mgr,
            message_mgr,
        )


@router.post("/ai/text/{session_id}")
async def ai_by_text(
        session_id: str,
        request_body: AITextRequest,
        taskpool: BackgroundTasks,
        ai_agent: AIAgentService = Depends(dependencies.get_ai_service_client),
        session_mgr: SessionManager = Depends(get_shared_session_manager),
        message_mgr: MessageManager = Depends(get_shared_message_manager),
):
    """HTTP阻塞响应接口，接收文本并返回AI流式响应"""
    ctx = None
    try:
        # 先初始化context
        ctx = Context.from_user_input(user_input=request_body.user_input, session_id=session_id)
        # 获取文本数据
        text = request_body.text
        res = {
            "text": "",
            "cmd": {}
        }
        async for chunk in agent_chat(ctx, text, ai_agent):
            if chunk.type in {ResponseType.Start, ResponseType.End}:
                continue
            if isinstance(chunk.content, dict):
                res["cmd"].update(chunk.content)
            elif isinstance(chunk.content, str):
                res["text"] += chunk.content
        return JSONResponse(
            content=res,
            headers={
                "X-Session-ID": session_id,  # TODO: 重新定义头
                "Cache-Control": "no-cache",  # 防止缓存
                "Connection": "keep-alive",  # 保持长连接
            },
        )
    except ujson.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format")
    except Exception as e:
        LOG.error("Failed to process request", extra={"error": str(e), "session": session_id}, exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={"error": str(e), "session_id": session_id, "timestamp": datetime.now().isoformat()},
        )
    finally:
        taskpool.add_task(
            gen_session_title_summary,
            session_id,
            ctx,
            ai_agent,
            session_mgr,
            message_mgr,
        )
