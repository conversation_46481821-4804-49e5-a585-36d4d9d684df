from typing import List
from fastapi import APIRouter, Depends

from business.chat_session.store import Message, MessageManager
from business.clients.chat_session import get_shared_message_manager
from models.manager import (
    APIErrorCode,
    APIResponse,
    FilterOperation,
    ManagerMessageDeleteArguments,
    ManagerMessageListUserAllArguments,
    ManagerMessageSearchArguments,
    ManagerMessageSentimentOperationArguments,
    PagingResponse,
)
from models.message import APIChatMessageEntity, MessageAttr, MessageAttrSentimentType, MessageRole
from utils.response import Response


router = APIRouter(prefix="/message")


def build_list_paging_query(user_id, prev_message, page_token, page_size, acquire_count, search_fields, filters, sorts):
    args = {
        "user_id": user_id,
        "prev_messages": prev_message,
        "page_token": page_token,
        "page_size": page_size,
        "acquire_count": acquire_count,
    }
    for filter_cond in filters:
        if filter_cond.field not in search_fields:
            continue
        match filter_cond.operation:
            case FilterOperation.Like:
                args[f"{filter_cond.field}_like"] = filter_cond.value
            case FilterOperation.Equal:
                args[filter_cond.field] = filter_cond.value
    if sorts:
        args["sorts"] = []
        for s in sorts:
            args["sorts"].append((s.field, s.order))
    return args


async def search_message(message_mgr, args):
    msgs, cnt, page_token = await message_mgr.api_search_messages(**args)
    return Response(
        APIResponse[PagingResponse[List[APIChatMessageEntity]]](
            code=APIErrorCode.OK,
            data=PagingResponse(
                page_token=page_token,
                total=cnt,
                result=msgs,
            ),
        )
    )


@router.post("/list")
async def list_messages(
    req: ManagerMessageSearchArguments,
    message_mgr: MessageManager = Depends(get_shared_message_manager),
):
    req.page_size *= 2
    SEARCH_FIELDS = ["message"]
    args = build_list_paging_query(
        req.user_id,
        req.prev_message,
        req.page_token,
        req.page_size,
        req.acquire_count,
        SEARCH_FIELDS,
        req.filters,
        req.sort,
    )
    if req.session_ids:
        args["session_ids"] = req.session_ids
    try:
        return await search_message(message_mgr, args)
    except Exception as e:
        return Response(APIResponse(code=APIErrorCode.InternalError, msg=str(e)))


@router.post("/list-user-all")
async def list_user_all_messages(
    req: ManagerMessageListUserAllArguments,
    message_mgr: MessageManager = Depends(get_shared_message_manager),
):
    req.page_size *= 2
    SEARCH_FIELDS = ["message"]
    args = build_list_paging_query(
        req.user_id,
        req.prev_message,
        req.page_token,
        req.page_size,
        req.acquire_count,
        SEARCH_FIELDS,
        req.filters,
        req.sort,
    )
    try:
        return await search_message(message_mgr, args)
    except Exception as e:
        return Response(APIResponse(code=APIErrorCode.InternalError, msg=str(e)))


@router.post("/meta/sentiment")
async def message_meta_sentiment(
    req: ManagerMessageSentimentOperationArguments,
    message_mgr: MessageManager = Depends(get_shared_message_manager),
):
    try:
        msg = await message_mgr.get_message(req.user_id, req.id)
        if msg is None:
            return Response(APIResponse(code=APIErrorCode.NotFound, msg="Message not found"))
        if msg.role != MessageRole.Assistant:
            return Response(APIResponse(code=APIErrorCode.InvalidArgument, msg="Invalid Message"))

        if req.like is not None:
            await message_mgr.set_message_ext_attr(
                req.id,
                {
                    MessageAttr.Sentiment: (
                        MessageAttrSentimentType.Like.value if req.like else MessageAttrSentimentType.Dislike.value
                    ),
                },
            )
        else:
            await message_mgr.del_message_ext_attr(
                req.id,
                [MessageAttr.Sentiment],
            )
        return Response(APIResponse(code=APIErrorCode.OK))
    except Exception as e:
        return Response(APIResponse(code=APIErrorCode.InternalError, msg=str(e)))


@router.post("/action/delete_by_ids")
async def message_delete_by_ids(
    req: ManagerMessageDeleteArguments,
    message_mgr: MessageManager = Depends(get_shared_message_manager),
):
    try:
        if not req.user_id or not req.message_ids:
            return Response(APIResponse(code=APIErrorCode.InvalidArgument, msg="Invalid Arguments"))

        await message_mgr.delete_messages(
            req.user_id,
            req.message_ids,
        )
        return Response(APIResponse(code=APIErrorCode.OK))

    except Exception as e:
        return Response(APIResponse(code=APIErrorCode.InternalError, msg=str(e)))
