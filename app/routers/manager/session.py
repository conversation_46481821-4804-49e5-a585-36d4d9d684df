import asyncio
from typing import List
from fastapi import APIRouter, Depends

from business.chat_session.store import (
    MessageManager,
    SessionInfo,
    SessionManager,
)
from business.clients.chat_session import get_shared_message_manager, get_shared_session_manager
from models.manager import (
    APIErrorCode,
    APIResponse,
    ManagerSessionDeleteArguments,
    ManagerSessionPagingArguments,
    FilterOperation,
    ManagerSessionTruncateMessageArguments,
    PagingResponse,
)
from utils.response import Response

router = APIRouter(prefix="/session")


@router.post("/list")
async def list_sessions(
    req: ManagerSessionPagingArguments,
    session_mgr: SessionManager = Depends(get_shared_session_manager),
):
    SEARCH_FIELDS = ["title"]
    args = {
        "user_id": req.user_id,
    }
    for filter_cond in req.filters:
        if filter_cond.field not in SEARCH_FIELDS:
            continue
        match filter_cond.operation:
            case FilterOperation.Like:
                args[f"{filter_cond.field}_like"] = filter_cond.value
            case FilterOperation.Equal:
                args[filter_cond.field] = filter_cond.value

    args["page_token"] = req.page_token
    args["page_size"] = req.page_size
    args["acquire_count"] = req.acquire_count

    try:
        sessions, total = await session_mgr.search_session(**args)

        return Response(
            APIResponse[PagingResponse[List[SessionInfo]]](
                code=APIErrorCode.OK,
                data=PagingResponse(
                    page_token=None if len(sessions) == 0 else sessions[-1].id,
                    total=total,
                    result=sessions,
                ),
            )
        )
    except Exception as e:
        return Response(APIResponse(code=APIErrorCode.InternalError, msg=str(e)))


@router.post("/action/delete_session")
async def delete_session(
    req: ManagerSessionDeleteArguments,
    session_mgr: SessionManager = Depends(get_shared_session_manager),
    message_mgr: MessageManager = Depends(get_shared_message_manager),
):
    try:
        await asyncio.gather(
            session_mgr.delete_session(req.user_id, req.session_id),
            message_mgr.delete_all_messages(req.user_id, req.session_id),
            return_exceptions=True,
        )
        return Response(APIResponse(code=APIErrorCode.OK))
    except Exception as e:
        return Response(APIResponse(code=APIErrorCode.InternalError, msg=str(e)))


@router.post("/action/truncate_session_message")
async def truncate_session_message(
    req: ManagerSessionTruncateMessageArguments,
    message_mgr: MessageManager = Depends(get_shared_message_manager),
):
    try:
        if not req.user_id or not req.session_id:
            return Response(APIResponse(code=APIErrorCode.InternalError, msg="user_id or session_id is empty"))

        await message_mgr.truncate_messages(
            req.user_id,
            req.session_id,
            req.max_size,
        )
        return Response(APIResponse(code=APIErrorCode.OK))
    except Exception as e:
        return Response(APIResponse(code=APIErrorCode.InternalError, msg=str(e)))
