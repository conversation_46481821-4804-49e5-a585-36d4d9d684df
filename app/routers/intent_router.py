# coding = utf-8
import time
from fastapi import API<PERSON>outer, Depends, HTTPException
from dependencies.service import get_intent_service_client
from models.common import AITextRequest, Context
from models.semantic_router import UserInput

from business.semantic.intent import IntentService

# 创建 FastAPI 路由器实例
router = APIRouter()


@router.post("/intent_process")
async def process_input(
    user_input: UserInput,
    intent_service: IntentService = Depends(get_intent_service_client),
):
    """
    处理用户输入文本，进行意图识别并返回结果。

    Args:
        user_input (UserInput): 包含用户输入文本的请求体

    Returns:
        ProcessResult: 包含意图识别结果的响应对象

    Raises:
        HTTPException: 当无法理解用户输入时抛出400错误
    """
    # 记录开始时间
    start_at = time.perf_counter()
    ctx = Context(session_id="")
    # 使用 IntentService 处理输入文本
    result = await intent_service.recognize_intent(ctx, user_input.text)

    # 计算处理耗时（毫秒）
    end_time = time.perf_counter()
    elapsed_time = int((end_time - start_at) * 1000)

    if result:
        return {"time_elapsed": elapsed_time, "details": result}
    else:
        # 当无法识别意图时抛出异常
        raise HTTPException(status_code=400, detail=f"Unable to understand the input: {user_input.text}")


@router.post("/semantic-intent/{session_id}")
async def semantic_intent(
    session_id: str,
    request_body: AITextRequest,
    intent_service: IntentService = Depends(get_intent_service_client),
):
    ctx = Context.from_user_input(user_input=request_body.user_input, session_id=session_id)

    # 获取文本数据
    text = request_body.text
    t0 = time.perf_counter()
    res = await intent_service.recognize_intent(ctx, text)
    res["time_elapsed"] = time.perf_counter() - t0
    if res["msg"]:
        raise HTTPException(res["msg"])
    return res
