# from fastapi import APIRouter, WebSocket, Depends
# from fastapi import File, UploadFile
# import ujson
# import dependencies
# from fastapi import HTTPException
# from datetime import datetime, timezone
# import time
# from utils import logging

# router = APIRouter()
# LOG = logging.getLogger(__name__)


# @router.post("/transcribe")
# async def transcribe(file: UploadFile = File(...), service=Depends(dependencies.get_transcriber_client)):
#     """使用文件上传的方式，接收音频文件，并返回转录结果"""
#     start_at = time.perf_counter()
#     process_times = {}

#     try:
#         # 记录文件信息
#         LOG.info(f"Processing file: {file.filename}")
#         LOG.info(f"Content-Type: {file.content_type}")

#         # 记录文件读取开始时间
#         read_start = time.perf_counter()
#         audio_bytes = await file.read()
#         process_times["file_read"] = time.perf_counter() - read_start

#         # 记录转录开始时间
#         transcribe_start = time.perf_counter()
#         transcription = await service(audio_bytes)
#         process_times["transcribe"] = time.perf_counter() - transcribe_start

#         # 计算总耗时
#         total_time = time.perf_counter() - start_at

#         # 记录完成信息
#         LOG.info(f"Successfully transcribed file: {file.filename}")
#         LOG.info(f"Total processing time: {total_time:.3f}s")

#         return {
#             "transcription": transcription,
#             "file_info": {
#                 "filename": file.filename,
#                 "content_type": file.content_type,
#                 "file_size": len(audio_bytes),
#             },
#             "timing_info": {
#                 "total_time": f"{total_time:.3f}s",
#                 "file_read_time": f"{process_times['file_read']:.3f}s",
#                 "transcribe_time": f"{process_times['transcribe']:.3f}s",
#                 "timestamp": datetime.now(timezone.utc).isoformat(),
#             },
#         }
#     except Exception as e:
#         end_time = time.perf_counter()
#         LOG.error(f"Failed to process file: {file.filename}", exc_info=True)
#         raise HTTPException(
#             status_code=400,
#             detail={
#                 "error": str(e),
#                 "file_info": {"filename": file.filename, "content_type": file.content_type},
#                 "total_time": f"{end_time - start_at:.2f}s",
#                 "timestamp": datetime.now(timezone.utc).isoformat(),
#             },
#         )


# @router.websocket("/ws/streaming/transcribe")
# async def ws_transcribe(websocket: WebSocket, service=Depends(dependencies.get_transcriber_client)):
#     """使用websocket的方式，接收音频分片，并返回转录结果"""
#     await websocket.accept()
#     session_id = f"session_{id(websocket)}"

#     try:
#         while True:  # 保持连接持续监听
#             # 接收客户端发送的音频分片
#             audio_chunk = await websocket.receive_bytes()

#             # 语音转文字
#             text = await service(session_id, audio_chunk)
#             if text:
#                 LOG.info(f"ws 转录: {text}")
#                 await websocket.send_text(
#                     ujson.dumps(
#                         {
#                             "type": "transcribe",
#                             "text": text,
#                         }
#                     )
#                 )

#     except Exception as e:
#         LOG.error(f"[{session_id}] Client error: {str(e)}")
#     finally:
#         await websocket.close()
