from copy import deepcopy
import logging
import logging.config
import os
from typing import Callable
import pythonjsonlogger.jsonlogger

from config.settings import EnvCategory, RunMode, settings
from models.common import Context


def init_logger():
    # 确保日志目录存在
    os.makedirs(settings.LOG_PATH, exist_ok=True)  # 新增目录创建

    debugRotatingConfig = {
        "class": "logging.handlers.TimedRotatingFileHandler",
        "when": "H",
        "interval": 1,
        "backupCount": 7 * 24,
        "encoding": "utf-8",
    }
    releaseRotatingConfig = {
        "class": "logging.handlers.TimedRotatingFileHandler",
        "when": "d",
        "interval": 1,
        "backupCount": 7,
        "encoding": "utf-8",
    }

    rotatingConfig = releaseRotatingConfig if settings.ENV == EnvCategory.RELEASE else debugRotatingConfig

    logging.config.dictConfig(
        {
            "version": 1,
            "disable_existing_loggers": True if settings.RUN_MODE != RunMode.RAY else False,  # Ray 不要动已有的，巨蠢
            "formatters": {
                "default": {
                    "format": "%(asctime)s - %(name)s[%(filename)s%(lineno)d] - %(levelname)s - %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S%z",
                },
                "json": {
                    "()": pythonjsonlogger.json.JsonFormatter,  # 指定自定义的 Formatter 类
                    "datefmt": "%Y-%m-%d %H:%M:%S%z",
                    "fmt": "{name}{process}{thread}{threadName}{taskName}{asctime}{exc_info}{filename}{funcName}{levelname}{lineno}{message}",  # 不需要分隔符，只是标记，告知 JSON formatter 使用什么字段
                    "style": "{",
                },
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "formatter": "default",
                    "level": settings.LOG_LEVEL.upper(),
                },
                "json-file": {
                    "filename": os.path.join(settings.LOG_PATH, "jarvis.log"),
                    "formatter": "json",
                    "level": settings.LOG_LEVEL.upper(),
                    **rotatingConfig,
                },
                "json-console": {
                    "class": "logging.StreamHandler",
                    "formatter": "json",
                    "level": settings.LOG_LEVEL.upper(),
                },
            },
            "loggers": {
                "jarvis": {
                    "handlers": ["json-file", "json-console"],
                    "level": settings.LOG_LEVEL.upper(),
                    "propagate": True,
                },
                "pymongo": {
                    "level": settings.LOG_LEVEL.upper(),
                    "propagate": True,
                }
            },
            "root": {
                "handlers": ["console"],
                "level": settings.LOG_LEVEL.upper(),
            },
        }
    )


def getLogger(name=None):
    root = logging.getLogger("jarvis")
    return root if name is None else root.getChild(name)


class LoggerAdapter(logging.LoggerAdapter):
    # 哎。。
    def process(self, msg, kwargs):
        clone_kwargs = deepcopy(kwargs)
        msg, transform_kwargs = super().process(msg, kwargs)
        if "extra" in clone_kwargs:
            transform_kwargs.setdefault("extra", {}).update(clone_kwargs["extra"])
        return msg, transform_kwargs
