# coding = utf-8
from datetime import datetime
from pytz import timezone, utc

# TODO: 
# - 支持多种时间戳格式
def get_local_time_str(timestamp_str: str, time_zone: str) -> str:
    if not timestamp_str or not time_zone:
        return ""
    # 原始解析（带微秒处理）
    utc_time = datetime.strptime(timestamp_str, "%Y-%m-%dT%H:%M:%S.%fZ")
    # 添加UTC时区标记
    utc_time = utc_time.replace(tzinfo=utc)

    # 转换为当地时区
    local_time = utc_time.astimezone(timezone(time_zone))

    # 转换为本地时间字符串
    return local_time.strftime("%Y-%m-%d %H:%M:%S")
