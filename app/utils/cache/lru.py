# coding = utf-8
from __future__ import annotations

import dataclasses
import enum
import time
from typing import Any, Optional, Dict, Hashable

from config.settings import settings


class LRU:
    @dataclasses.dataclass
    class Node:
        expired_timestamp: int | float
        key: Optional[Hashable]
        next: Optional[LRU.Node] = None
        prev: Optional[LRU.Node] = None
        value: Any = None

        def is_expired(self):
            if self.expired_timestamp < time.time() and self.expired_timestamp != -1:
                return True
            return False

    def __init__(self, max_size, ttl_s):
        self._ttl_s = ttl_s
        self._max_size = max_size
        self._head = LRU.Node(expired_timestamp=-1, key=None)
        self._tail = LRU.Node(expired_timestamp=-1, prev=self._head, key=None)
        self._head.next = self._tail
        self._mapping: Dict[Hashable, LRU.Node] = {}

    def _move_to_head(self, node):
        if node.next:
            node.next.prev = node.prev
        if node.prev:
            node.prev.next = node.next
        if self._head.next:
            self._head.next.prev = node
        node.next = self._head.next
        self._head.next = node
        node.prev = self._head

    def get(self, key, default=None):
        node: LRU.Node = self._mapping.get(key)
        if node is None:
            return default
        if node.expired_timestamp >= time.time():
            result = node.value
            self._move_to_head(node)
            return result
        while self._tail.prev.is_expired():
            self.remove_last_node()
        return default

    def remove_last_node(self):
        remove_node = self._tail.prev
        remove_node.prev.next = self._tail
        self._tail.prev = remove_node.prev
        remove_node.prev = None
        remove_node.next = None
        self._mapping.pop(remove_node.key)
        del remove_node

    def set(self, key, value, ttl_s=None):
        if ttl_s is None:
            ttl_s = self._ttl_s
        node = self._mapping.get(key)
        if node:
            node.value = value
            node.expired_timestamp = time.time() + ttl_s
            self._move_to_head(node)
            return
        node = LRU.Node(expired_timestamp=time.time() + ttl_s, value=value, key=key)
        while self._tail.prev.is_expired():
            self.remove_last_node()
        if len(self._mapping) >= self._max_size:
            self.remove_last_node()
        self._move_to_head(node)
        self._mapping[key] = node


class Manager:
    def __init__(self):
        self._dbs: Dict[str, LRU] = {}

    def register_lru(self, db_name, *args, **kwargs):
        self._dbs[db_name] = LRU(*args, **kwargs)

    def get_db(self, db_name):
        return self._dbs.get(db_name)


class DBS(enum.Enum):
    SEARCH = "search"
    LOCATION = "location"
    NEWS = "news"


lru_manager = Manager()
lru_manager.register_lru(DBS.SEARCH, max_size=settings.SEARCH_CACHE_MAX_SIZE, ttl_s=settings.SEARCH_CACHE_TTL)
lru_manager.register_lru(DBS.LOCATION, max_size=settings.LOCATION_CACHE_MAX_SIZE, ttl_s=settings.LOCATION_CACHE_TTL)
lru_manager.register_lru(DBS.NEWS, max_size=settings.NEWS_CACHE_MAX_SIZE, ttl_s=settings.NEWS_CACHE_TTL)
