from functools import wraps


def Singleton(cls):
    """
    Decorator to make a class a singleton
    """

    def singleton(*args, **kwargs):
        if not hasattr(cls, "_singleton_instance"):
            cls._singleton_instance = {}
        if cls.__qualname__ not in cls._singleton_instance:
            cls._singleton_instance[cls.__qualname__] = cls(*args, **kwargs)
        return cls._singleton_instance[cls.__qualname__]

    return singleton
