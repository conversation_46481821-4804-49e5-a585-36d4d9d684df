import abc
import time
import httpx
from utils import logging

LOG = logging.getLogger(__name__)


class HttpClient:
    """
    该类只是用于封装 Httpx 的 Session，做一些基础防呆和全局配置
    """

    def __init__(
        self,
        *,
        ssl_verify=False,
        timeout=10,
        base_url=None,
        limit=httpx.Limits(
            max_connections=1000,
            max_keepalive_connections=500,
            keepalive_expiry=60
        ),
        auth=None,
    ):
        """
        @param ssl_verify: 是否验证 SSL 证书
        @param timeout: 整个请求超时时间
        @param base_url: 请求的基础 URL


        auth 目前不提供外部构造器了，暂时还没有这么复杂的场景
        headers 提供一些通用的 Request
        """
        self._client = httpx.AsyncClient(
            verify=ssl_verify,
            timeout=timeout,
            base_url=base_url or "",
            limits=limit,
            auth=auth,
        )

    async def parse_response(self, resp: httpx.Response):
        if resp is None:
            return None
        if resp.status_code != 200:
            LOG.error(f"query failed due to {resp.text}, status_code = {resp.status_code}")
            return None
        return await self.transform_response(resp)

    @abc.abstractmethod
    async def transform_response(self, resp: httpx.Response): ...

    async def method(
        self,
        method: str,
        url: httpx.URL | str,
        *,
        content=None,
        data=None,
        files=None,
        json=None,
        params=None,
        headers=None,
        cookies=None,
        follow_redirects=True,
    ):
        resp = None
        start_at = time.perf_counter()
        try:
            resp = await self._client.request(
                method,
                url,
                content=content,
                data=data,
                files=files,
                json=json,
                params=params,
                headers=headers,
                cookies=cookies,
                follow_redirects=follow_redirects,
            )
        except Exception as e:
            LOG.debug(f"{method} {url} failed due to {e}")
            raise e
        finally:
            LOG.debug(f"{method} {url} cost: {time.perf_counter() - start_at:.2f}s")

        return await self.parse_response(resp)


class JSONHttpClient(HttpClient):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    async def transform_response(self, resp: httpx.Response):
        try:
            return resp.json()
        except Exception as e:
            LOG.error(f"parse response failed due to {e}, resp = {resp.text}")
            return None
