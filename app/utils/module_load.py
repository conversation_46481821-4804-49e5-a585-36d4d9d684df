import importlib.util
import inspect
import sys
from importlib import import_module
from pathlib import Path
from pkgutil import iter_modules
from types import ModuleType


def import_module_from_file(filepath: Path) -> ModuleType:
    module_name = filepath.stem
    spec = importlib.util.spec_from_file_location(module_name, filepath)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module


def cached_import(module_path, class_name):
    modules = sys.modules
    if module_path not in modules or (
        # Module is not fully initialized.
        getattr(modules[module_path], "__spec__", None) is not None
        and getattr(modules[module_path].__spec__, "_initializing", False) is True
    ):
        import_module(module_path)
    return getattr(modules[module_path], class_name)


def import_string(dotted_path):
    """
    Import a dotted module path and return the attribute/class designated by the
    last name in the path. Raise ImportError if the import failed.
    """
    try:
        module_path, class_name = dotted_path.rsplit(".", 1)
    except ValueError as err:
        raise ImportError("%s doesn't look like a module path" % dotted_path) from err

    try:
        return cached_import(module_path, class_name)
    except AttributeError as err:
        raise ImportError('Module "%s" does not define a "%s" attribute/class' % (module_path, class_name)) from err


def load_all_modules(modules_path, module_cls, field="__name__"):
    all_modules = {}
    for module in walk_modules(modules_path):
        mods_loaded = load_modules_dict(module, module_cls, field)
        all_modules.update(mods_loaded)
    return all_modules


def walk_modules(path):
    mods = []
    mod = import_module(path)
    mods.append(mod)
    if hasattr(mod, "__path__"):
        for _, subpath, ispkg in iter_modules(mod.__path__):
            fullpath = path + "." + subpath
            if ispkg:
                mods += walk_modules(fullpath)
            else:
                submod = import_module(fullpath)
                mods.append(submod)
    return mods


def load_modules_dict(module, module_cls, field):
    return {getattr(cls, field): cls for cls in iter_module_classes(module, module_cls, field)}


def iter_module_classes(module, module_cls, field):
    for obj in vars(module).values():
        if (
            inspect.isclass(obj)
            and issubclass(obj, module_cls)
            and obj.__module__ == module.__name__
            and getattr(obj, field, None)
        ):
            yield obj
