# coding = utf-8
from fastapi import FastAPI

from fastapi_app import FastApiFactory
from handler.transcribe import TranscriberServe
from business.semantic.intent import SemanticIntentDeployment
from business.ai.ai_agent import AIAgentDeployment
from config.settings import RunMode, settings
from utils import logging


def get_ray(app: FastApiFactory):
    from ray import serve

    @serve.deployment(max_ongoing_requests=settings.MAX_ONGOING_REQUESTS)
    @serve.ingress(app.get_app())
    class FastAPIWrapper:
        def __init__(self, *args):
            logging.init_logger()
            logging.getLogger().info("Starting FastAPIWrapper")

    entry_app = FastAPIWrapper.bind(TranscriberServe.bind(), AIAgentDeployment.bind(), SemanticIntentDeployment.bind())
    return entry_app


def ray_local(entry_app):
    from ray import serve

    serve.run(entry_app, blocking=True)


def fastapi_main(app: FastApiFactory):
    import uvicorn

    config = uvicorn.Config(app.get_app(), host="0.0.0.0", port=8000, log_level=settings.LOG_LEVEL.lower())
    server = uvicorn.Server(config)
    server.run()


def main(ray_conf):
    logging.init_logger()
    logging.getLogger().info("Current configuration", extra=settings.model_dump(mode="json"))
    app_factory = FastApiFactory().enable_cors(cors_allow_domains=["*"]).enable_prometheus().setup()
    if settings.RUN_MODE == RunMode.RAY:
        logging.getLogger().info("Starting in Ray mode")
        return get_ray(app_factory)
    elif settings.RUN_MODE == RunMode.RAY_LOCAL:
        logging.getLogger().info("Starting in Ray local mode")
        return ray_local(get_ray(app_factory))
    else:
        logging.getLogger().info("Starting in FastAPI mode")
        return fastapi_main(app_factory)


if __name__ == "__main__":
    main({})
