from business.chat_session.store import Message<PERSON>ana<PERSON>, SessionManager
from business.clients.middlewares import get_shared_mongo_client, get_shared_redis_client
from config.settings import settings

# 暂时先这样。。后续改掉

_shared_session_manager = None


def get_shared_session_manager() -> SessionManager:

    global _shared_session_manager
    if not _shared_session_manager:
        _shared_session_manager = SessionManager(get_shared_mongo_client())
    return _shared_session_manager


_shared_message_manager = None


def get_shared_message_manager() -> MessageManager:
    global _shared_message_manager
    if not _shared_message_manager:
        _shared_message_manager = MessageManager(
            get_shared_mongo_client(),
            get_shared_redis_client(),
        )
    return _shared_message_manager
