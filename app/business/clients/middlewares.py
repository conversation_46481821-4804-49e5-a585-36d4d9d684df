import redis.asyncio as redis
from config.settings import settings
from motor.motor_asyncio import AsyncIOMotorClient
import redis.asyncio as redis

_shared_redis_client = None


def get_shared_redis_client() -> redis.Redis:
    global _shared_redis_client
    if not _shared_redis_client:
        redis_client_kwargs = {
            "host": settings.REDIS_HOST,
            "port": settings.REDIS_PORT,
            "db": settings.CACHE_MESSAGE_LIST_DB,
        }
        if settings.REDIS_PASSWORD:
            redis_client_kwargs["password"] = settings.REDIS_PASSWORD
        _shared_redis_client = redis.Redis(
            **redis_client_kwargs,
        )
    return _shared_redis_client


_shared_mongo_client = None


def get_shared_mongo_client() -> AsyncIOMotorClient:
    global _shared_mongo_client
    if not _shared_mongo_client:
        _shared_mongo_client = AsyncIOMotorClient(settings.MONGO_URI)
    return _shared_mongo_client
