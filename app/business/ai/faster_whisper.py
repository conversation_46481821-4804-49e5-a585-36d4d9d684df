# # coding = utf-8
# import sys
# from faster_whisper import WhisperModel, BatchedInferencePipeline

# from config.settings import settings
# from utils import logging

# LOG = logging.getLogger(__name__)

# # TODO: 避免下载模型
# # 选择合适大小的模型，在速度和准确性之间权衡
# MODEL_SIZE = "small"


# def initialize_faster_whisper():
#     try:

#         # 首先加载到 CPU
#         model = WhisperModel(
#             MODEL_SIZE,
#             device="cpu",
#             cpu_threads=settings.FASTER_WHISPER_THREADS,
#             num_workers=settings.FASTER_WHISPER_WORKERS,
#         )
#         model = BatchedInferencePipeline(model=model)
#         LOG.info(f"Faster whisper model initialized successfully on {MODEL_SIZE}")
#         return model

#     except Exception as e:
#         LOG.error(
#             "Failed to initialize Faster Whisper model", exc_info=True, extra={"model_size": MODEL_SIZE, "err": e}
#         )
#         sys.exit(1)


# 初始化模型
# faster_whisper_model = initialize_faster_whisper()
