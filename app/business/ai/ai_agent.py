# coding = utf-8
import datetime
import hashlib
import time
from pydantic import Field, BaseModel
from ray import serve
from typing import AsyncGenerator, Optional, Union
import sys
import ujson
from business.ai.backend.base import AIServiceBackend
from business.ai.memory import ChattingMemory
from business.ai.tools import Tool
from config.settings import settings
from core.ray_helper import DeploymentMixin
from dependencies.service import get_intent_service_client
from models.common import Context, ResponseType
from models.message import Message, MessageCategory, MessageRole
from utils import logging
from utils.contexts import combined_contexts
from utils.module_load import import_string, load_all_modules
from core import metrics

LOG = logging.getLogger(__name__)


class AIAgentResponseChunk(BaseModel):
    type: ResponseType = Field(ResponseType.Text, example=ResponseType.Text)
    content: Optional[Union[str, dict]] = Field(None, example="Hello, world!")


class AIAgentService:
    ACTIVE_BACKEND = import_string(settings.AIAGENT_BACKEND)

    def __init__(self):
        self._tools = [
            tool
            for _, tool in load_all_modules("business.ai.tools", Tool).items()
            if tool.__name__ != Tool.__name__
            and hasattr(sys.modules["business.ai.tools"], "__all__")
            and tool.__name__ in sys.modules["business.ai.tools"].__all__
        ]
        self._client: AIServiceBackend = self.ACTIVE_BACKEND(self._tools)
        self._intent_service = get_intent_service_client()

    @staticmethod
    def _usr_txt_msg(ctx: Context, round_key: str, text: str):
        return Message(
            session_id=ctx.session_id,
            user_id=ctx.user_id,
            role=MessageRole.User,
            round_key=round_key,
            message_category=MessageCategory.Text,
            content=text,
        )

    @staticmethod
    def _assistant_txt_msg(ctx: Context, round_key: str, text: str):
        return Message(
            session_id=ctx.session_id,
            user_id=ctx.user_id,
            role=MessageRole.Assistant,
            round_key=round_key,
            message_category=MessageCategory.Text,
            content=text,
        )

    @staticmethod
    def _intent_msg(ctx: Context, round_key, resp):
        return Message(
            session_id=ctx.session_id,
            user_id=ctx.user_id,
            role=MessageRole.Assistant,
            round_key=round_key,
            message_category=MessageCategory.Intent,
            content=ujson.dumps(resp),
        )

    @staticmethod
    def log_extra(ctx: Context, **kwargs):
        return {
            "session_id": ctx.session_id,
            "user_id": ctx.user_id,
            **kwargs,
        }

    async def prepare_message_input(self, ctx: Context, text: str, memory: ChattingMemory, metric, round_key):
        with combined_contexts(metrics.time_consumed(metric, metrics.CHATTING_MESSAGE_PROCESSING_TIME)):
            usr_input = self._usr_txt_msg(ctx, round_key, text)
            await memory.add(usr_input, blocking=False)
            history = await memory.get()

            if len(history) == 0:
                LOG.error(
                    "History message obtained is empty, fallback to default",
                    extra=self.log_extra(
                        ctx,
                        round_key=round_key,
                    ),
                )
                history = [usr_input]

            return history, len(history)

    async def process_stream(self, ctx: Context, text: str) -> AsyncGenerator[AIAgentResponseChunk, None]:
        round_key = hashlib.md5(
            f"{ctx.session_id}_{ctx.user_id}_{text}_{datetime.datetime.now().timestamp()}".encode()
        ).hexdigest()

        m = metrics.ChattingMetric(ctx, self._client.name, self._client.model)
        metrics.CHATTING_REQUESTS.inc(m)

        memory = ChattingMemory(ctx.user_id, ctx.session_id)

        extra_info = {
            "backend_name": self._client.name,
            "backend_model": self._client.model,
            "input": text,
            "input_len": len(text.split()),
            "response_len": 0,
            "response": "",
            "response_type": "",
            "round_key": round_key,
        }

        debug_res_info = []
        full_response = []

        with combined_contexts(
            metrics.in_progress(m, metrics.CHATTING_REQUESTS_IN_PROGRESS),
            metrics.time_consumed(m, metrics.CHATTING_PROCESSING_TIME),
        ):

            start_at = time.perf_counter()
            debug_res_info.append(
                f"{self._client.name.upper()}({self._client.model}) input[{text}]({extra_info['input_len']})"
            )

            # 基于nlp的快速意图识别
            start_intent_time = time.perf_counter()
            intent_data = await self.intent_recognize(ctx, text)
            if intent_data:
                elapsed_time = time.perf_counter() - start_intent_time
                is_intent_supported = intent_data.get("is_supported") or False
                res_type = ResponseType.Cmd if is_intent_supported else ResponseType.Text
                res = intent_data if is_intent_supported else "Unsupported Function"
                yield AIAgentResponseChunk(type=res_type, content=res)
                debug_res_info.append(f"[NLP INTENT] hit[{elapsed_time:.2f}s]: {intent_data['intent']}")
                debug_res_info.append(f"score[{intent_data['score']*100:.2f}%]")
                debug_res_info.append(f"entities[{intent_data['entities'] if intent_data['entities'] else 'n/a'}]")
                extra_info["response_type"] = f"{res_type.value}_nlp"
                extra_info["intent"] = intent_data
                LOG.info(" | ".join(debug_res_info), extra=self.log_extra(ctx, **extra_info))

                await memory.add([self._usr_txt_msg(ctx, round_key, text),
                                  self._intent_msg(ctx, round_key, intent_data) if is_intent_supported else self._assistant_txt_msg(ctx, round_key, res)],
                                 False)
                return

            get_intent_time = time.perf_counter() - start_intent_time
            debug_res_info.append(f"[NLP INTENT] miss[{get_intent_time:.2f}s]")

            history_msg, history_msg_len = await self.prepare_message_input(ctx, text, memory, m, round_key)

            try:
                system_prompt = await self._client.system_prompt(ctx)
                first_token_exists = False
                # 流式获取响应
                async for result in self._client.send_chat_stream(
                    ctx,
                    system_prompt,
                    history_msg,
                ):
                    # 处理连续换行的情况
                    # Gemini会返回连续的换行符，需要处理
                    response_type, chunk = result[0], result[1]
                    str_chunk = isinstance(chunk, str)
                    chunk = " " if str_chunk and chunk.strip() == "" else chunk

                    # 如果结果是cmd，则写入intent类message
                    if response_type == ResponseType.Cmd:
                        extra_info["response_type"] = f"{ResponseType.Cmd.value}_ai"
                        debug_res_info.append(f"[LLM INTENT] hit: {chunk['intent']}, entities: {chunk['entities']}")
                        extra_info["intent"] = chunk
                        await memory.add(self._intent_msg(ctx, round_key, chunk), False)

                    if not first_token_exists:
                        first_token_time = time.perf_counter() - start_at
                        first_token_len = len(chunk.split()) if str_chunk else 0
                        debug_res_info.append(f"first({first_token_time:.2f}s)({first_token_len})")
                        first_token_exists = True
                        metrics.CHATTING_FIRST_RESPONSE_TIME.observe(m, first_token_time)

                    yield AIAgentResponseChunk(type=response_type, content=chunk)

                    # TODO: 暂时如此恶心实现，暂时想不到什么好的方法
                    if str_chunk:
                        full_response.append(chunk)

            except Exception as e:
                LOG.error(
                    "[ERROR] Failed to process AI streaming request | " + " | ".join(debug_res_info),
                    extra=self.log_extra(ctx, err=str(e), **extra_info),
                    exc_info=True,
                )
                yield AIAgentResponseChunk(type=ResponseType.Text, content="Failed to process the request")
            finally:
                # 如果没有任何响应，则返回默认的错误响应
                if not full_response and extra_info["response_type"] not in [
                    f"{ResponseType.Cmd.value}_ai",
                    f"{ResponseType.Cmd.value}_nlp",
                ]:
                    empty_response = "I'm having trouble understanding your request. Please try again."
                    yield AIAgentResponseChunk(type=ResponseType.Text, content=empty_response)
                    full_response.append(empty_response)

                full_rsp = "".join(full_response)
                full_rsp_len = len(full_rsp.split())
                full_rsp_time = time.perf_counter() - start_at
                debug_res_info.append(f"full({full_rsp_time:.2f}s)({full_rsp_len})")
                debug_res_info.append(f"round #{(history_msg_len//2) + 1}")
                extra_info["response_type"] = (
                    f"{ResponseType.Text.value}_ai" if not extra_info["response_type"] else extra_info["response_type"]
                )
                extra_info["response"] = full_rsp[:200]
                extra_info["response_len"] = full_rsp_len
                LOG.info("[TRACE] " + " | ".join(debug_res_info), extra=self.log_extra(ctx, **extra_info))
                if full_rsp:
                    await memory.add(self._assistant_txt_msg(ctx, round_key, full_rsp), False)

    async def intent_recognize(self, ctx: Context, text: str):
        # 意图识别
        m = metrics.IntentMetric(ctx)
        with combined_contexts(
            metrics.in_progress(m, metrics.INTENT_IN_PROGRESS),
            metrics.time_consumed(m, metrics.INTENT_PROCESSING_TIME),
        ):
            try:
                intent_data = await self._intent_service.recognize_intent(ctx, text)
            except Exception as e:
                LOG.error(f"Failed to recognize intent: {str(e)}", exc_info=True, extra=self.log_extra(ctx, err=str(e)))
                return None

            # 统计分布
            if intent_data:
                intent_m = metrics.IntentCategoryMetric(ctx, intent=intent_data["intent"])
                metrics.INTENT_COUNTER.inc(intent_m)
                metrics.INTENT_DISTRIBUTION.observe(intent_m, intent_data["score"] * 100)
            return intent_data


@serve.deployment(max_ongoing_requests=settings.MAX_ONGOING_REQUESTS)
class AIAgentDeployment(DeploymentMixin):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._ai_agent_service = AIAgentService()

    async def process_stream(
        self,
        ctx: Context,
        text: str,
    ) -> AsyncGenerator[str, None]:
        async for resp in self._ai_agent_service.process_stream(ctx, text):
            yield resp
