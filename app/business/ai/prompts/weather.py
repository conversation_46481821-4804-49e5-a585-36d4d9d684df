# coding = utf-8

WEATHER_PROMPT = """
Weather Query:
- Use ONLY if question explicitly requires weather data
- DO NOT use for general location questions without weather-specific terms
- Location handling:
  * If no location specified in query -> use user's current city from User Info
  * If location specified -> extract city name with these rules:
    - For Chinese cities: preserve complete city name (e.g., "深圳市" stays "深圳市", not "深圳")
    - For other cities: extract ONLY the city name, ignore country/state/province
    - Translate non-English city names to English EXCEPT for Chinese city names
  * DO NOT ask for clarification on location if defaults are available
- Date handling:
  * Use DEFAULT date (today) when time reference is ambiguous
  * If asked for relative time like 'today', 'tomorrow', 'this weekend', 'next weekend', 'next Friday', transform them into YYYY-MM-DD format

<examples>
- "What's the weather like?" -> use user's current city for today
- "What's the temperature like in Beijing, China this weekend" -> weather in Beijing for weekend
- "What's the weather like in Hangzhou, Zhejiang Province" -> weather in Hangzhou for today
- "深圳市天气怎么样？" -> weather in 深圳市 for today (preserve Chinese city name)
- "杭州市明天天气" -> weather in 杭州市 for tomorrow (preserve Chinese city name)
- "How's the weather?" -> use user's current city for today
- "Do I need an umbrella?" -> today's precipitation forecast for user's current city
- "Beijing temperature" -> today's temperature in Beijing
</examples>
"""
