# coding = utf-8
import os
import ssl
from typing import AsyncGenerator, List, Dict, Any

import certifi
import httpx
from google import genai
from google.genai import types
from google.genai.types import HttpOptions

from business.ai.tools import Tool
from config.settings import settings
from models.common import Context, ResponseType
from models.message import Message, MessageRole, MessageToolCall, MessageToolCallFunction, MessageToolCallResult
from utils import logging
from .llm import ContentGenerator, ToolProvider

LOG = logging.getLogger(__name__)


class GeminiToolAdapter(ToolProvider):

    def _convert_tool(self, in_: Tool):
        return types.Tool(
            function_declarations=[
                types.FunctionDeclaration(
                    name=in_.name,
                    description=in_.description,
                    parameters=types.Schema(
                        **in_.parameters,
                    ),
                )
            ]
        )


class GeminiMessageConverter:

    @staticmethod
    def parse(msg: Message):
        if msg.role == MessageRole.User:
            return GeminiMessageConverter.parse_user(msg)
        elif msg.role == MessageRole.Assistant:
            return GeminiMessageConverter.parse_assistant(msg)
        else:
            return []

    @staticmethod
    def parse_user(msg: Message):
        return [types.UserContent(types.Part.from_text(text=msg.content))]

    @staticmethod
    def parse_assistant(msg: Message):
        if msg.tool_calls is None:
            return [types.ModelContent(types.Part.from_text(text=msg.content))]
        else:
            return GeminiMessageConverter.parse_tool_call(msg)

    @staticmethod
    def parse_tool_call(msg: Message):
        # 拆分成 CallReq 和 CallResp
        call_req_parts = []
        call_resp_parts = []

        for tc in msg.tool_calls:
            call_req_parts.append(
                types.Part.from_function_call(
                    name=tc.function.name,
                    args=tc.function.args,
                )
            )
            call_resp_parts.append(
                types.Part.from_function_response(
                    name=tc.function.name,
                    response={"output": tc.function.result.result},  # 如果有 error 就用 error 做key，具体参考 API 文档
                )
            )

        return [
            types.ModelContent(call_req_parts),
            types.UserContent(call_resp_parts),
        ]


class GeminiContentGenerator(ContentGenerator):

    def __init__(
            self,
            ctx: Context,
            client: genai.Client,
            history: List[Message],
            tools: GeminiToolAdapter,
            # Gemini config
            model: str,
            system_prompt: str,
            temperature: float = 0.5,
            max_tokens: int = 1000,
    ):
        super().__init__(history)
        self._client = client
        self._ctx = ctx
        self._tools = tools

        safety_settings = [
            types.SafetySetting(
                category=types.HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                threshold=types.HarmBlockThreshold.BLOCK_ONLY_HIGH,
            ),
            types.SafetySetting(
                category=types.HarmCategory.HARM_CATEGORY_HARASSMENT,
                threshold=types.HarmBlockThreshold.BLOCK_ONLY_HIGH,
            ),
            types.SafetySetting(
                category=types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                threshold=types.HarmBlockThreshold.BLOCK_ONLY_HIGH,
            ),
            types.SafetySetting(
                category=types.HarmCategory.HARM_CATEGORY_CIVIC_INTEGRITY,
                threshold=types.HarmBlockThreshold.BLOCK_ONLY_HIGH,
            ),
            types.SafetySetting(
                category=types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                threshold=types.HarmBlockThreshold.BLOCK_ONLY_HIGH,
            ),
        ]

        self._completions_opts = {
            "model": model,
            "config": types.GenerateContentConfig(
                system_instruction=system_prompt,
                temperature=temperature,
                max_output_tokens=max_tokens,
                tools=self._tools.get_tools(),
                safety_settings=safety_settings,
            ),
        }

        self._tool_exec_context = self._tools.create_executor()

    async def _pre_send_request(self):
        self._tool_exec_context.reset()

    async def _post_response_completed(self):
        # 无工具，直接结束啦
        if self._tool_exec_context.no_func_call:
            self._stop = True
            return

        func_call_msg = Message(
            session_id=self._ctx.session_id,
            user_id=self._ctx.user_id,
            role=MessageRole.Assistant,
            content="",
            tool_calls=[],
        )

        for result, args in await self._tools.execute(self._ctx, self._tool_exec_context):
            if result.direct_output:
                LOG.info(f"[{self._ctx.session_id}] Direct output: function {args.name} output {result.result}")
                self._stop = True
                # 强制输出
                return result.result_type, result.result
            # 工具消息
            func_call_msg.tool_calls.append(
                MessageToolCall(
                    id=args.id,
                    type="function",
                    function=MessageToolCallFunction(
                        name=args.name,
                        args=args.args,
                        result=MessageToolCallResult(
                            result=result.result,
                        ),
                    ),
                )
            )

        self._msg.append(func_call_msg)

    async def _send_request(self, messages: List[Message]):
        input_msg = []
        for m in self._msg:
            input_msg.extend(GeminiMessageConverter.parse(m))
        return await self._client.aio.models.generate_content_stream(
            contents=input_msg,
            **self._completions_opts,
        )

    async def _parse_response_chunk(self, chunk: types.GenerateContentResponse):
        if not chunk.candidates:
            return
        candidate = chunk.candidates[0]
        if not candidate.content:
            return

        for part in candidate.content.parts:
            if part.function_call:
                self._tool_exec_context.execute(
                    part.function_call.name,
                    part.function_call.args,
                    part.function_call,
                )
            if part.text:
                yield (ResponseType.Text, part.text)


def GeminiToolConverter(in_: Tool):
    return types.Tool(
        function_declarations=[
            types.FunctionDeclaration(
                name=in_.name,
                description=in_.description,
                parameters=types.Schema(
                    **in_.parameters,
                ),
            )
        ]
    )


class GeminiClient:

    def __init__(self):
        self._ssl_ctx = ssl.create_default_context(
            cafile=os.environ.get('SSL_CERT_FILE', certifi.where()),
            capath=os.environ.get('SSL_CERT_DIR'),
        )
        self.client = genai.Client(api_key=settings.GOOGLE_AI_API_KEY, http_options=HttpOptions(
            async_client_args={
                "verify": self._ssl_ctx,
                "limits": httpx.Limits(
                    max_connections=1000,
                    max_keepalive_connections=500,
                    keepalive_expiry=60
                )
            }
        ))

    async def send_chat_stream(
            self,
            ctx: Context,
            model: str,
            system_prompt: str,
            messages: List[Dict[str, Any]],
            temperature: float = 0.7,
            max_tokens: int = 1000,
            tools: dict[str, Tool] = None,
            tool_choice: str = None,
    ) -> AsyncGenerator[str, None]:
        """
        Async streaming generation with tools support
        """

        async for x in GeminiContentGenerator(
                ctx, self.client, messages, tools, model, system_prompt, temperature=temperature, max_tokens=max_tokens
        ).chat():
            yield x
        return


shared_gemini_client = GeminiClient()
