# coding = utf-8
import u<PERSON><PERSON>
from typing import Async<PERSON>enerator, List, Dict, Any
from openai import Async<PERSON>penA<PERSON>
from openai.types.chat.chat_completion_chunk import Chat<PERSON>ompletionChunk, ChoiceDelta
from business.ai.tools import Tool
from config.settings import settings
from models.common import Context, ResponseType
from models.message import Message, MessageRole, MessageToolCall
from .llm import ContentGenerator, ToolProvider
from utils import logging

LOG = logging.getLogger(__name__)


class OepnAIToolAdapter(ToolProvider):

    def _convert_tool(self, in_: Tool):
        return {
            "type": "function",
            "function": {
                "name": in_.name,
                "description": in_.description,
                "parameters": in_.parameters,
            },
        }


class OpenAIContentGenerator(ContentGenerator):

    def __init__(
        self,
        ctx: Context,
        client: AsyncOpenAI,
        history: List[Message],
        tools: OepnAIToolAdapter,
        # OpenAI config
        model: str,
        system_prompt: str,
        temperature: float = 0.7,
        max_tokens: int = 1000,
        tool_choice: str = "auto",
    ):
        super().__init__(history)
        self._client = client
        self._tools = tools
        self._ctx = ctx

        self._completions_opts = {
            "model": model,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": True,
            "tools": self._tools.get_tools(),
            "tool_choice": tool_choice,
        }
        self._system_prompt = Message(
            session_id="",
            user_id="",
            role=MessageRole.System,
            content=system_prompt,
        )
        self._func_call_ctx = tools.create_executor()

    @staticmethod
    def _msg_converter(in_: Message):
        match in_.role:
            case MessageRole.System:
                return {
                    "role": "system",
                    "content": in_.content,
                }
            case MessageRole.User:
                return {
                    "role": "user",
                    "content": in_.content,
                }
            case MessageRole.Assistant:
                ret = {
                    "role": "assistant",
                    "content": in_.content,
                }
                if in_.tool_calls:
                    ret["tool_calls"] = [
                        {
                            "id": x.id,
                            "type": "function",
                            "function": {
                                "name": x.function.name,
                                "arguments": ujson.dumps(x.function.args),
                            },
                        }
                        for x in in_.tool_calls
                    ]
                return ret
            case MessageRole.Tools:
                return {
                    "role": "tool",
                    "content": in_.content,
                    "tool_call_id": in_.tool_call_id,
                }
            case _:
                return None

    async def _send_request(self, messages: List[Message]):
        # 开始发送消息
        return await self._client.chat.completions.create(
            messages=[x for x in [self._msg_converter(m) for m in [self._system_prompt] + self._msg] if x is not None],
            **self._completions_opts,
        )

    async def _pre_send_request(self):
        self._func_call_ctx.reset()
        self._func_call_builder = []

    async def _post_response_completed(self):
        for x in self._func_call_builder:
            self._func_call_ctx.execute(x.function.name, ujson.loads(x.function.arguments), x)
        # 检查是否有函数调用
        if self._func_call_ctx.no_func_call:
            self._stop = True
            return

        func_call_msg = Message(
            session_id=self._ctx.session_id,
            user_id=self._ctx.user_id,
            role=MessageRole.Assistant,
            content="",
            tool_calls=[],
        )
        func_call_response = []

        for result, args in await self._tools.execute(self._ctx, self._func_call_ctx):
            if result.direct_output:
                LOG.info(
                    f"[{self._ctx.session_id}] Direct output: function {args.function.name} output {result.result}"
                )
                self._stop = True
                return (result.result_type, result.result)

            func_call_msg.tool_calls.append(  # 结果追加
                MessageToolCall(
                    id=args.id,
                    type="function",
                    function=args.function.model_dump(mode="json"),
                )
            )

            func_call_response.append(
                Message(
                    session_id=self._ctx.session_id,
                    user_id=self._ctx.user_id,
                    role=MessageRole.Tools,
                    content=result.result,
                    tool_call_id=args.id,
                    name=args.function.name,
                )
            )

        # 构造历史
        self._msg.append(func_call_msg)
        self._msg += func_call_response

        # 防止调用递归，这里对工具进行限制
        self._completions_opts["tool_choice"] = "none"

    async def _parse_response_chunk(self, chunk: ChatCompletionChunk):
        chunk = await super()._parse_response_chunk(chunk)
        if not chunk.choices:
            return
        choice = chunk.choices[0]
        delta = choice.delta

        if delta.tool_calls:
            await self._parse_tool_call(delta)

        if delta.content:
            yield (ResponseType.Text, delta.content)

    async def _parse_tool_call(self, delta: ChoiceDelta):
        for tool_call in delta.tool_calls:
            if tool_call.id:
                self._func_call_builder.append(tool_call)
            else:
                self._func_call_builder[-1].function.arguments += tool_call.function.arguments or ""


class BaseLLMClient:

    def __init__(self, api_key: str, base_url: str):
        self._async_client = AsyncOpenAI(api_key=api_key, base_url=base_url)

    # TODO:
    # - 丰富该函数
    async def send_chat_stream(
        self,
        ctx: Context,
        model: str,
        system_prompt: str,
        messages: List[Dict[str, Any]],
        temperature: float = 0.7,
        max_tokens: int = 1000,
        tools: dict[str, Tool] = None,
        tool_choice: str = "auto",
    ) -> AsyncGenerator[str, None]:
        """
        流式chat回复
        """

        async for x in OpenAIContentGenerator(
            ctx,
            self._async_client,
            messages,
            tools,
            model,
            system_prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            tool_choice=tool_choice,
        ).chat():
            yield x


class MistralClient(BaseLLMClient):
    name = "mistral"

    def __init__(self):
        super().__init__(
            api_key=settings.MISTRAL_API_KEY,
            base_url=settings.MISTRAL_BASE_URL,
        )


shared_mistral_client = MistralClient()
