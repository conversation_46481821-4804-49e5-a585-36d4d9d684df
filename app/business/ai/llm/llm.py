import asyncio
from typing import Any, <PERSON>, Optional, Tuple
from business.ai.tools import Tool
from business.chat_session.store import Message
from models.common import Context, ToolResult


class ContentGenerator:
    def __init__(self, input_msg: List[Message], *args, **kwargs):
        self._stop = False
        self._msg = input_msg

    async def chat(self, in_msg: Optional[Message] = None):
        if in_msg is not None:
            self._msg.append(in_msg)
        while not self._stop:
            # Send Chat
            await self._pre_send_request()
            response = await self._send_request(self._msg)
            # parse response
            parsed_response = await self._parse_response(response)
            # parse response chunk
            async for chunk in parsed_response:
                async for result in self._parse_response_chunk(chunk):
                    yield result
            ret = await self._post_response_completed()
            if ret is not None:
                yield ret

    async def _pre_send_request(self): ...

    async def _post_response_completed(self): ...

    async def _send_request(self, messages: List[Message]):
        # 真实消息发送
        ...

    async def _parse_response(self, rsp):
        return rsp

    async def _parse_response_chunk(self, chunk):
        return chunk

    def final_messages(self):
        return self._msg


class ToolProvider:

    class ExecutorContext:
        def __init__(self):
            self.func_call = []

        def execute(self, tool_name, tool_args, addition_data=None):
            self.func_call.append((tool_name, tool_args, addition_data))

        def reset(self):
            self.func_call = []

        @property
        def no_func_call(self):
            return len(self.func_call) == 0

    def __init__(self, tools: List[Tool] = [], *args, **kwargs):
        self._tools = tools
        self._converted_tools = [self._convert_tool(x) for x in tools]
        self._tool_instances = {x.name: x() for x in tools}

    def _convert_tool(self, tool: Tool): ...

    def get_tools(self):
        return self._converted_tools

    def create_executor(self):
        return ToolProvider.ExecutorContext()

    async def execute(self, ctx: Context, executor) -> List[Tuple[ToolResult, Any]]:
        tasks = []
        for name, args, addition_data in executor.func_call:
            if name not in self._tool_instances:
                tasks.append(asyncio.sleep(0, (ToolResult(result=""), addition_data)))
            else:
                result = await self._tool_instances[name].run(ctx=ctx, **args)

                async def result_wrapper(val, addition_data):
                    if asyncio.iscoroutine(val):
                        val = await val
                    return (val, addition_data)

                tasks.append(result_wrapper(result, addition_data))
        return await asyncio.gather(*tasks, return_exceptions=True)
