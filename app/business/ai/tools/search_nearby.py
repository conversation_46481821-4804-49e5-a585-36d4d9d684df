# coding = utf-8
from business.ai.tools import Tool
from config.settings import settings
from models.common import Context, ToolResult
from client import google_places_client
from business.ai.prompts import RESPONSE_PROMPT_TEMPLATE, attach_response_length_prompt
from utils import logging

LOG = logging.getLogger(__name__)

PLACE_TYPES = [
    # 汽车
    "car_dealer",
    "car_rental",
    "car_repair",
    "car_wash",
    "electric_vehicle_charging_station",
    "gas_station",
    "parking",
    "rest_stop",
    # 企业
    "corporate_office",
    "farm",
    "ranch",
    # 文化
    "art_gallery",
    "art_studio",
    "auditorium",
    "cultural_landmark",
    "historical_place",
    "monument",
    "museum",
    "performing_arts_theater",
    "sculpture",
    # 教育
    "library",
    "preschool",
    "primary_school",
    "school",
    "secondary_school",
    "university",
    # 休闲娱乐场所
    "adventure_sports_center",
    "amphitheatre",
    "amusement_center",
    "amusement_park",
    "aquarium",
    "banquet_hall",
    "barbecue_area",
    "botanical_garden",
    "bowling_alley",
    "casino",
    "childrens_camp",
    "comedy_club",
    "community_center",
    "concert_hall",
    "convention_center",
    "cultural_center",
    "cycling_park",
    "dance_hall",
    "dog_park",
    "event_venue",
    "ferris_wheel",
    "garden",
    "hiking_area",
    "historical_landmark",
    "internet_cafe",
    "karaoke",
    "marina",
    "movie_rental",
    "movie_theater",
    "national_park",
    "night_club",
    "observation_deck",
    "off_roading_area",
    "opera_house",
    "park",
    "philharmonic_hall",
    "picnic_ground",
    "planetarium",
    "plaza",
    "roller_coaster",
    "skateboard_park",
    "state_park",
    "tourist_attraction",
    "video_arcade",
    "visitor_center",
    "water_park",
    "wedding_venue",
    "wildlife_park",
    "wildlife_refuge",
    "zoo",
    # 设施
    "public_bath",
    "public_bathroom",
    "stable",
    # 金融
    "accounting",
    "atm",
    "bank",
    # 餐饮
    "acai_shop",
    "afghani_restaurant",
    "african_restaurant",
    "american_restaurant",
    "asian_restaurant",
    "bagel_shop",
    "bakery",
    "bar",
    "bar_and_grill",
    "barbecue_restaurant",
    "brazilian_restaurant",
    "breakfast_restaurant",
    "brunch_restaurant",
    "buffet_restaurant",
    "cafe",
    "cafeteria",
    "candy_store",
    "cat_cafe",
    "chinese_restaurant",
    "chocolate_factory",
    "chocolate_shop",
    "coffee_shop",
    "confectionery",
    "deli",
    "dessert_restaurant",
    "dessert_shop",
    "diner",
    "dog_cafe",
    "donut_shop",
    "fast_food_restaurant",
    "fine_dining_restaurant",
    "food_court",
    "french_restaurant",
    "greek_restaurant",
    "hamburger_restaurant",
    "ice_cream_shop",
    "indian_restaurant",
    "indonesian_restaurant",
    "italian_restaurant",
    "japanese_restaurant",
    "juice_shop",
    "korean_restaurant",
    "lebanese_restaurant",
    "meal_delivery",
    "meal_takeaway",
    "mediterranean_restaurant",
    "mexican_restaurant",
    "middle_eastern_restaurant",
    "pizza_restaurant",
    "pub",
    "ramen_restaurant",
    "restaurant",
    "sandwich_shop",
    "seafood_restaurant",
    "spanish_restaurant",
    "steak_house",
    "sushi_restaurant",
    "tea_house",
    "thai_restaurant",
    "turkish_restaurant",
    "vegan_restaurant",
    "vegetarian_restaurant",
    "vietnamese_restaurant",
    "wine_bar",
    # 地理区域
    "administrative_area_level_1",
    "administrative_area_level_2",
    "country",
    "locality",
    "postal_code",
    "school_district",
    # 政府
    "city_hall",
    "courthouse",
    "embassy",
    "fire_station",
    "government_office",
    "local_government_office",
    "neighborhood_police_station",
    "police",
    "post_office",
    # 健康与保健
    "chiropractor",
    "dental_clinic",
    "dentist",
    "doctor",
    "drugstore",
    "hospital",
    "massage",
    "medical_lab",
    "pharmacy",
    "physiotherapist",
    "sauna",
    "skin_care_clinic",
    "spa",
    "tanning_studio",
    "wellness_center",
    "yoga_studio",
    # 住房
    "apartment_building",
    "apartment_complex",
    "condominium_complex",
    "housing_complex",
    # 住宿
    "bed_and_breakfast",
    "budget_japanese_inn",
    "campground",
    "camping_cabin",
    "cottage",
    "extended_stay_hotel",
    "farmstay",
    "guest_house",
    "hostel",
    "hotel",
    "inn",
    "japanese_inn",
    "lodging",
    "mobile_home_park",
    "motel",
    "private_guest_room",
    "resort_hotel",
    "rv_park",
    # 自然景观
    "beach",
    # 礼拜场所
    "church",
    "hindu_temple",
    "mosque",
    "synagogue",
    # 服务
    "astrologer",
    "barber_shop",
    "beautician",
    "beauty_salon",
    "body_art_service",
    "catering_service",
    "cemetery",
    "child_care_agency",
    "consultant",
    "courier_service",
    "electrician",
    "florist",
    "food_delivery",
    "foot_care",
    "funeral_home",
    "hair_care",
    "hair_salon",
    "insurance_agency",
    "laundry",
    "lawyer",
    "locksmith",
    "makeup_artist",
    "moving_company",
    "nail_salon",
    "painter",
    "plumber",
    "psychic",
    "real_estate_agency",
    "roofing_contractor",
    "storage",
    "summer_camp_organizer",
    "tailor",
    "telecommunications_service_provider",
    "tour_agency",
    "tourist_information_center",
    "travel_agency",
    "veterinary_care",
    # 购物
    "asian_grocery_store",
    "auto_parts_store",
    "bicycle_store",
    "book_store",
    "butcher_shop",
    "cell_phone_store",
    "clothing_store",
    "convenience_store",
    "department_store",
    "discount_store",
    "electronics_store",
    "food_store",
    "furniture_store",
    "gift_shop",
    "grocery_store",
    "hardware_store",
    "home_goods_store",
    "home_improvement_store",
    "jewelry_store",
    "liquor_store",
    "market",
    "pet_store",
    "shoe_store",
    "shopping_mall",
    "sporting_goods_store",
    "store",
    "supermarket",
    "warehouse_store",
    "wholesaler",
    # 体育
    "arena",
    "athletic_field",
    "fishing_charter",
    "fishing_pond",
    "fitness_center",
    "golf_course",
    "gym",
    "ice_skating_rink",
    "playground",
    "ski_resort",
    "sports_activity_location",
    "sports_club",
    "sports_coaching",
    "sports_complex",
    "stadium",
    "swimming_pool",
    # 交通
    "airport",
    "airstrip",
    "bus_station",
    "bus_stop",
    "ferry_terminal",
    "heliport",
    "international_airport",
    "light_rail_station",
    "park_and_ride",
    "subway_station",
    "taxi_stand",
    "train_station",
    "transit_depot",
    "transit_station",
    "truck_stop",
]


SEARCH_NEARBY_RESPONSE_PROMPT = """
    Your task is to generate proper response about nearby search result.
    {shared_prompt}
    <search_result>
    {result}
    </search_result>
"""


class SearchNearbyTool(Tool):
    name = "search_nearby"
    description = "Find nearby places"
    parameters = {
        "type": "object",
        "properties": {
            "included_types": {
                "type": "array",
                "description": 'includedTypes in the searchNearby API of Google Places, e.g., ["restaurant", "school"]',
                "items": {"type": "string", "enum": PLACE_TYPES},
                "minItems": 1,
            }
        },
        "required": ["included_types"],
    }

    async def call(self, ctx: Context, included_types: list) -> ToolResult:
        radius_meter = settings.SEARCH_NEARBY_RADIUS_METER
        lat = ctx.location.latitude
        lng = ctx.location.longitude
        LOG.info(f"Tool called: SearchNearby | included_types[{included_types}] | lat[{lat}] | lng[{lng}] | radius_meter[{radius_meter}]", extra=self.log_extra(ctx, keywords=included_types, lat=lat, lng=lng, radius_meter=radius_meter))
        result_list = await google_places_client.search_nearby(
            included_types,
            center_latitude=ctx.location.latitude,
            center_longitude=ctx.location.longitude,
            radius_meter=radius_meter,
        )

        if not result_list:
            return self.result(result="No related nearby places found", direct_output=True)

        result = "".join(
            [
                f'{item.get("displayName", {}).get("text", "")}: {item.get("shortFormattedAddress", "")}'
                for item in result_list
            ]
        )
        # shared_prompt = attach_response_length_prompt(RESPONSE_PROMPT_TEMPLATE, ctx)
        return self.result(
            result=SEARCH_NEARBY_RESPONSE_PROMPT.format(shared_prompt=RESPONSE_PROMPT_TEMPLATE, result=result)
        )
