from datetime import datetime, timedelta
from typing import Optional
import pytz

from business.ai.tools import Tool
from models.common import Context, ToolResult
from client import openweather_client
from business.ai.prompts import RESPONSE_PROMPT_TEMPLATE, attach_response_length_prompt
from utils import logging

LOG = logging.getLogger(__name__)

WEATHER_RESPONSE_PROMPT = """
    You are a weather assistant, your jobs are:
    - Summarize weather information to create concise and informative weather report
    - Provide thoughtful dressing advice based on the weather query result
    - Provide temperature in both Celsius and Fahrenheit
    {shared_prompt}
    <weather_result>
    {result}
    </weather_result>
"""


def parse_relative_date(ctx: Context, date_str: str) -> datetime:
    """Parse relative time descriptions like 'today', 'tomorrow', 'this weekend'"""
    local_time = datetime.strptime(ctx.local_time, "%Y-%m-%d %H:%M:%S")
    if hasattr(ctx, 'timezone') and ctx.timezone:
        try:
            tz = pytz.timezone(ctx.timezone)
            local_time = local_time.replace(tzinfo=tz)
        except pytz.exceptions.UnknownTimeZoneError:
            LOG.warning(f"Unknown timezone: {ctx.timezone}, using naive datetime")
    
    today = local_time.replace(hour=0, minute=0, second=0, microsecond=0)
    lower_str = date_str.lower()

    if lower_str == "today":
        return today
    if lower_str == "tomorrow":
        return today + timedelta(days=1)
    if "weekend" in lower_str:
        # 找到下一个周六或周日
        days_until_saturday = (5 - today.weekday()) % 7
        saturday = today + timedelta(days=days_until_saturday)
        return saturday if days_until_saturday <= 4 else saturday - timedelta(weeks=1)

    result_date = datetime.strptime(date_str, "%Y-%m-%d")
    if hasattr(ctx, 'timezone') and ctx.timezone and local_time.tzinfo:
        try:
            tz = pytz.timezone(ctx.timezone)
            result_date = tz.localize(result_date)
        except pytz.exceptions.UnknownTimeZoneError:
            pass
    
    return result_date


class WeatherTool(Tool):
    name = "get_weather"
    description = "Get the current weather for a specific location"
    parameters = {
        "type": "object",
        "properties": {
            "location": {
                "type": "string",
                "description": "The English name of the city or location, e.g., Beijing",
            },
            "date": {
                "type": "string",
                "description": "The date string in YYYY-MM-DD format",
            },
        },
        "required": ["location", "date"],
    }

    async def call(self, ctx: Context, location: str, date: str) -> ToolResult:
        search_location = location or ctx.location.city
        fn = None

        try:
            if search_location and date and parse_relative_date(ctx, date).strftime("%Y-%m-%d") != date:
                fn = "forecast"
                result = await self.get_forecast_weather(ctx, search_location, date)
            elif search_location:
                fn = "current"
                result = await self.get_current_weather(search_location)
            else:
                result = ToolResult(result="Location is missing", direct_output=True)
        except Exception as e:
            LOG.error("Fetch weather data failed", extra=self.log_extra(ctx, error=str(e)))
            result = ToolResult(result=f"Weather data unavailable for {search_location} at {date}.", direct_output=True)

        if not result:
            result = ToolResult(result=f"Weather data unavailable for {search_location} at {date}.", direct_output=True)
        LOG.info(
            f"Tool called: WeatherTool | location[{search_location}] | date[{date}] | local_time[{ctx.local_time}] | fn[{fn}]",
            extra=self.log_extra(
                ctx,
                location=search_location,
                date=date,
                local_time=ctx.local_time,
            ),
        )
        return result

    @classmethod
    async def get_forecast_weather(cls, ctx: Context, search_location, date: Optional[str] = None):
        # 解析相对时间或标准日期
        target_date = parse_relative_date(ctx, date)
        
        # Get current date with proper timezone handling
        current_date = datetime.strptime(ctx.local_time, "%Y-%m-%d %H:%M:%S")
        if hasattr(ctx, 'timezone') and ctx.timezone:
            try:
                tz = pytz.timezone(ctx.timezone)
                current_date = current_date.replace(tzinfo=tz)
            except pytz.exceptions.UnknownTimeZoneError:
                LOG.warning(f"Unknown timezone: {ctx.timezone}, using naive datetime")
                
        days_diff = (target_date.date() - current_date.date()).days
        # 非法时间范围检查
        if not (0 <= days_diff <= 4):
            res = f"Only supports querying weather for the next 5 days (requested date: {target_date.strftime('%Y-%m-%d')})"
            LOG.info(f"{res}", extra=cls.log_extra(ctx))
            return ToolResult(result=res)

        # 使用预测api
        forecast_data = await openweather_client.get_forecast_weather(search_location, target_date)
        if not forecast_data:
            return None

        # 拼接结果
        forecast = forecast_data["forecast"]
        forecast_time = datetime.fromtimestamp(forecast["dt"])
        temp_celsius = forecast['main']['temp'] or 0
        feels_like_celsius = forecast['main']['feels_like'] or 0
        humidity = forecast['main']['humidity'] or 0
        wind_speed = forecast['wind']['speed'] or 0
        temp_fahrenheit = round(temp_celsius * 9/5 + 32, 2)
        feels_like_fahrenheit = round(feels_like_celsius * 9/5 + 32, 2)
        res = (
            f"Weather in {forecast_data['city']['name']} on {forecast_time.strftime('%Y-%m-%d %H:%M')}: "
            f"{forecast['weather'][0]['description']}. "
            f"Temperature: {temp_celsius}°C or {temp_fahrenheit}°F (Feels like {feels_like_celsius}°C or {feels_like_fahrenheit}°F). "
            f"Humidity: {humidity}%, Wind: {wind_speed} m/s."
        )
        # shared_prompt = attach_response_length_prompt(RESPONSE_PROMPT_TEMPLATE, ctx)
        return ToolResult(result=WEATHER_RESPONSE_PROMPT.format(shared_prompt=RESPONSE_PROMPT_TEMPLATE, result=res))

    @classmethod
    async def get_current_weather(cls, location):
        current_data = await openweather_client.get_current_weather(location)
        if not current_data:
            return None
        temp_celsius = current_data['main']['temp'] or 0
        feels_like_celsius = current_data['main']['feels_like'] or 0
        humidity = current_data['main']['humidity']
        wind_speed = current_data['wind']['speed']
        temp_fahrenheit = round(temp_celsius * 9/5 + 32, 2)
        feels_like_fahrenheit = round(feels_like_celsius * 9/5 + 32, 2)
        res = (
            f"Current weather in {current_data['name']}: "
            f"{current_data['weather'][0]['description']}. "
            f"Temperature: {temp_celsius}°C or {temp_fahrenheit}°F (Feels like {feels_like_celsius}°C or {feels_like_fahrenheit}°F). "
            f"Humidity: {humidity}%, Wind: {wind_speed} m/s."
        )
        return ToolResult(result=WEATHER_RESPONSE_PROMPT.format(shared_prompt=RESPONSE_PROMPT_TEMPLATE, result=res))
