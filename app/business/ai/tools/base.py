from typing import Union, Dict
from core import metrics
from models.common import Context, ToolResult, ResponseType
import time
from utils import logging
from utils.contexts import combined_contexts

LOG = logging.getLogger(__name__)


class Tool:
    name: str = ""  # 工具名称
    description: str = ""  # 工具描述
    parameters: dict = {}  # 工具参数, 完全对接 openai 的 tools 格式，其他基于该格式 wrapper
    raise_exception = True  # 是否抛出异常, 默认行为

    def __init__(self): ...

    async def call(self, ctx: Context, *args, **kwargs) -> ToolResult: ...

    @classmethod
    def log_extra(cls, ctx: Context, **kwargs):
        return {
            "session_id": ctx.session_id,
            "user_id": ctx.user_id,
            "tool": cls.name,
            **kwargs,
        }

    async def run(self, ctx: Context, *args, **kwargs) -> ToolResult:
        """
        运行工具
        :param ctx: 上下文
        :param args: 其他参数
        :param kwargs: 其他参数
        :return: 工具结果
        """
        start_at = time.perf_counter()
        result = None
        try:
            m = metrics.ChattingToolMetric(ctx, self.name)
            metrics.CHATTING_TOOL_REQUESTS.inc(m)
            with combined_contexts(
                metrics.in_progress(m, metrics.CHATTING_TOOL_REQUESTS_IN_PROGRESS),
                metrics.time_consumed(m, metrics.CHATTING_TOOL_PROCESSING_TIME),
            ):
                result = await self.call(ctx, *args, **kwargs)
            LOG.info(
                f"CallTool: {self.name} took {time.perf_counter() - start_at:.2f}s",
                extra=self.log_extra(
                    ctx,
                    arguments=(args, kwargs),
                ),
            )
            return result
        except Exception as e:
            LOG.error(
                f"CallTool: {self.name} failed due to {e} took {time.perf_counter() - start_at:.2f}s",
                extra=self.log_extra(
                    ctx,
                    error=str(e),
                    arguments=(args, kwargs),
                ),
            )
            if self.raise_exception:
                raise e
            else:
                return self.empty_result()

    def result(
        self, result: Union[str, Dict], result_type: ResponseType = ResponseType.Text, direct_output: bool = False
    ) -> ToolResult:
        return ToolResult(result=result, result_type=result_type, direct_output=direct_output)

    def empty_result(self) -> ToolResult:
        return ToolResult(result=None, result_type=ResponseType.Text, direct_output=False)
