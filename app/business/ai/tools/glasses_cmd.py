# coding = utf-8

import enum
from typing import Optional, Dict
from business.ai.tools import Tool
from models.common import Context, ToolResult, ResponseType
from models.intent import IntentCategory
from utils import logging
from config.settings import settings

LOG = logging.getLogger(__name__)

# 特殊字符列表
# 暂时只用断句
# 谨慎调整，会影响意图识别
SEPARATORS_LIST = "?.,。，？!！"

# TODO: 从IntentCategory中获取
GLASSES_CMD_TYPES = [
    "disp_bright_set",  # 显示亮度-设置
    "disp_bright_inc",  # 显示亮度-增加
    "disp_bright_dec",  # 显示亮度-减少
    "disp_bright_max",  # 显示亮度-最大
    "disp_bright_min",  # 显示亮度-最小
    "disp_bright_auto_on",  # 显示亮度-自动开启
    "disp_bright_auto_off",  # 显示亮度-自动关闭
    "disp_dist_set",  # 显示距离-设置
    "disp_dist_inc",  # 显示距离-增加
    "disp_dist_dec",  # 显示距离-减少
    "disp_dist_max",  # 显示距离-最大
    "disp_dist_min",  # 显示距离-最小
    "disp_ht_set",  # 显示高度-设置
    "disp_ht_inc",  # 显示高度-增加
    "disp_ht_dec",  # 显示高度-减少
    "disp_ht_max",  # 显示高度-最大
    "disp_ht_min",  # 显示高度-最小
    "disp_on",  # 显示-开启
    "dp_on",  # Direct Push-开启
    "dp_off",  # Direct Push-关闭
    "notify_on",  # 消息通知-开启
    "notify_off",  # 消息通知-关闭
    "navi_on",  # 导航-开启
    "qn_on",  # 快速笔记-开启
    "silent_on",  # 静音模式-开启
    "telep_on",  # 提词器-开启
    "transc_on",  # 语音转文字-开启
    "transl_on",  # 实时翻译-开启
]

LANGUAGE_CODE = [
    "ar-AE",  # 阿拉伯语（阿联酋）
    "bn-IN",  # 孟加拉语（印度）
    "zh-HK",  # 粤语（中国香港）
    "zh-CN",  # 中文（中国大陆）
    "cs-CZ",  # 捷克语（捷克）
    "da-DK",  # 丹麦语（丹麦）
    "nl-NL",  # 荷兰语（荷兰）
    "en-US",  # 英语（美国）
    "fi-FI",  # 芬兰语（芬兰）
    "fr-FR",  # 法语（法国）
    "de-DE",  # 德语（德国）
    "de-CH",  # 德语（瑞士）
    "hi-IN",  # 印地语（印度）
    "it-IT",  # 意大利语（意大利）
    "ja-JP",  # 日语（日本）
    "ko-KR",  # 韩语（韩国）
    "nb-NO",  # 挪威语（挪威）
    "pl-PL",  # 波兰语（波兰）
    "pt-PT",  # 葡萄牙语（葡萄牙）
    "ru-RU",  # 俄语（俄罗斯）
    "es-ES",  # 西班牙语（西班牙）
    "sv-SE",  # 瑞典语（瑞典）
    "tr-TR",  # 土耳其语（土耳其）
    "uk-UA",  # 乌克兰语（乌克兰）
]

# 校验规则映射表（min_value, max_value, step）
VALUE_SET_VALIDATION_RULES = {
    IntentCategory.DisplayBrightness_SetTo: (0, 100, 1),
    IntentCategory.DisplayDistance_SetTo: (1, 5, 0.5),
    IntentCategory.DisplayHeight_SetTo: (1, 9, 1),
}

INTEGER_VALUE_SET_VALIDATION = {
    IntentCategory.DisplayBrightness_Incr,
    IntentCategory.DisplayDistance_Incr,
    IntentCategory.DisplayHeight_Incr,
    IntentCategory.DisplayBrightness_Decr,
    IntentCategory.DisplayDistance_Decr,
    IntentCategory.DisplayHeight_Decr,
}


class DestEnum(str, enum.Enum):
    Home = "home"
    Office = "office"


class TranspEnum(str, enum.Enum):
    Walk = "walk"
    Bike = "bike"


SYN_HOME = [
    # English
    "home", "my place", "house", "back home", "home address", "apartment", "residence", "my family",
    
    # 中文繁體
    "家", "家裡", "家裡面", "回家", "公寓", "我家", "我住的地方", "住宅", "寓所", "住處", "住所", "居所", "宅邸",
    
    # 中文简体
    "家", "家里", "家里面", "回家", "公寓", "我家", "我住的地方", "住宅", "寓所", "住处", "住所", "居所", "宅邸"
]

SYN_OFFICE = [
    # English
    "office", "work", "company", "office address", "office building", "workplace", "business",
    
    # 中文繁體
    "公司", "公司地址", "公司大楼", "办公室", "办公室地址", "办公室大楼", "辦公大樓", "辦公地址", "辦公場所", "工作地點", "商務大樓", "工作場所",
    
    # 中文简体
    "公司", "公司地址", "公司大楼", "办公室", "办公室地址", "办公室大楼", "办公大楼", "办公地址", "办公场所", "工作地点", "商务大楼", "工作场所"
]

SYN_TRANSP_WALK = [
    # English
    "walk", "walking", "by walking", "on foot", "foot", "feet",
    
    # 中文繁體
    "步行", "徒步", "走路", "行走", "步行走", "徒步走", "步行前往", "徒步旅行", "步行方式", "行路", "步輦", "徙步", "步程", "腳行",
    
    # 中文简体
    "步行", "徒步", "走路", "行走", "步行走", "徒步走", "步行前往", "徒步旅行", "步行方式", "行路", "步辇", "徙步", "步程", "脚行"
]

SYN_TRANSP_BIKE = [
    # English
    "bike", "cycling", "cycle", "bicycle", "by bike", "biking",
    "ride a bike", "bike ride", "cycling trip", "take the bicycle",
    "go cycling", "on a bicycle", "pedal", "pedaling",
    
    # 中文繁體
    "騎車", "騎單車", "騎自行車", "騎腳踏車", "踩單車", "單車出行",
    "踏單車", "單車代步", "腳踏車出行", "騎單車通勤", "單車通勤",
    
    # 中文简体  
    "骑车", "骑单车", "骑自行车", "骑脚踏车", "踩单车", "单车出行",
    "踏单车", "单车代步", "脚踏车出行", "骑单车通勤", "单车通勤"
]

FILTER_INTENT = [
    IntentCategory.noop
]

UNSUPPORTED_INTENT = [
    IntentCategory.QuickNote_On,
]


# TODO:
# 1. 优化这坨恶心代码，不要if else
# 2. 增加转录和翻译的语言实体
# 3. enum的使用有问题
def validate_glasses_cmd(intent_data: dict) -> Dict | None:
    intent = intent_data.get("intent")

    # 过滤指令
    if intent in FILTER_INTENT:
        return None

    entities = intent_data.get("entities") or {}

    # 需要数值修正的意图处理
    if intent in VALUE_SET_VALIDATION_RULES:
        value = entities.get("value")
        if value is None:
            return None

        try:
            value = float(value)
        except (ValueError, TypeError):
            return None

        min_val, max_val, step = VALUE_SET_VALIDATION_RULES[intent]

        # 数值修正逻辑
        # 1. 按步长对齐
        if step != 0:
            steps = round((value - min_val) / step)
            clamped_value = min_val + steps * step
        else:
            clamped_value = value

        # 2. 限制在[min, max]范围内
        clamped_value = max(min(clamped_value, max_val), min_val)

        # 3. 处理整数类型需求
        if step == 1:
            clamped_value = int(clamped_value)

        # 更新修正后的值
        entities["value"] = clamped_value
        intent_data["entities"] = entities

    if intent in INTEGER_VALUE_SET_VALIDATION:
        value = entities.get("value")
        if value is None or (isinstance(value, str) and not value.isdigit()):
            entities.pop("value", None)
        else:
            entities["value"] = int(value)
            intent_data["entities"] = entities
    
    if intent == IntentCategory.Translate_On:
        if "from_lan" in entities and entities["from_lan"] not in LANGUAGE_CODE:
            return None
        if "to_lan" in entities and entities["to_lan"] not in LANGUAGE_CODE:
            return None

    if intent == IntentCategory.Transcribe_On:
        if "from_lan" in entities:
            entities.pop("from_lan", None)
        if "to_lan" in entities and entities["to_lan"] not in LANGUAGE_CODE:
            return None

    # 修正导航意图
    if intent == IntentCategory.Navi_On:
        # 不接受空的目的地
        if "dest" not in entities or not entities["dest"]:
            return None

        dest = str(entities["dest"]).strip().strip(SEPARATORS_LIST).lower()

        # 暂时只支持home和office
        is_dest_home = any(syn in dest for syn in SYN_HOME)
        is_dest_office = any(syn in dest for syn in SYN_OFFICE)
        if is_dest_home:
            entities["dest"] = DestEnum.Home.value
        elif is_dest_office:
            entities["dest"] = DestEnum.Office.value
        else:
            return None

        if "transp" not in entities or not entities["transp"]:
            entities["transp"] = TranspEnum.Walk.value  # 默认步行
        
        transp = str(entities["transp"]).strip().strip(SEPARATORS_LIST).lower()
        is_transp_walk = any(syn in transp for syn in SYN_TRANSP_WALK)
        is_transp_bike = any(syn in transp for syn in SYN_TRANSP_BIKE)
        if is_transp_walk:
            entities["transp"] = TranspEnum.Walk.value
        elif is_transp_bike:
            entities["transp"] = TranspEnum.Bike.value
        else:
            return None

        intent_data["entities"] = entities

    intent_data["is_supported"] = intent not in UNSUPPORTED_INTENT

    # 置信度检查
    score = intent_data.get("score", 1.0)
    if score <= settings.SEMANTIC_THRESHOLD:
        return None

    return intent_data


class GlassesCmdTool(Tool):
    name = "gen_glasses_cmd"
    description = "Generate Glasses Command"
    parameters = {
        "type": "object",
        "properties": {
            "command": {
                "type": "string",
                "description": 'Glasses command abbreviation, e.g., "disp_bright_set", "notify_off"',
                "enum": GLASSES_CMD_TYPES,
            },
            "entities": {
                "type": "object",
                "description": "Glasses command entities parameters",
                "properties": {
                    "value": {
                        "type": "number",
                        "description": "The value of some settings command",
                    },
                    "dest": {
                        "type": "string",
                        "description": "The destination of navigation command, required for navigation command only, only 'home', 'office'",
                        "enum": [dest.value for dest in DestEnum],
                    },
                    "transp": {
                        "type": "string",
                        "description": "The transport of navigation command, required for navigation command only, only 'walk', 'bike'",
                        "enum": [transp.value for transp in TranspEnum],
                    },
                    "from_lan": {
                        "type": "string",
                        "description": "The ISO 639-1 and ISO 3166-1 language code to translate from",
                        "enum": LANGUAGE_CODE,
                    },
                    "to_lan": {
                        "type": "string",
                        "description": "The ISO 639-1 and ISO 3166-1 language code to translate/transcribe to",
                        "enum": LANGUAGE_CODE,
                    },
                },
                "required": [],
            },
        },
        "required": ["command"],
    }

    async def call(self, ctx: Context, command: str, entities: Optional[dict] = {}) -> ToolResult:
        intent_data = {
            "intent": command,
            "entities": entities,
        }

        validated_intent_data = validate_glasses_cmd(intent_data)
        '''
        LOG.info(
            f"Tool called: GlassesCmdTool | cmd[{command}] | entities[{entities}]| [LLM INTENT] validate_glasses_cmd: before {intent_data}, after {validated_intent_data}",
            extra=self.log_extra(ctx, entities=entities, command=command),
        )
        '''
        res_type = ResponseType.Cmd if validated_intent_data and validated_intent_data['is_supported'] else ResponseType.Text
        res = validated_intent_data if validated_intent_data and validated_intent_data['is_supported'] else "Unsupported Function"

        return self.result(result=res, direct_output=True, result_type=res_type)
