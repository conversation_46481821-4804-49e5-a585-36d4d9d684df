# coding = utf-8
from business.ai.tools import Tool
from models.common import Context, ToolResult
from client import google_places_client
from utils.cache.lru import lru_manager, DBS
from business.ai.prompts import RESPONSE_PROMPT_TEMPLATE, attach_response_length_prompt
from utils import logging

LOG = logging.getLogger(__name__)


SEARCH_LOCATION_RESPONSE_PROMPT = """
    Your task is to generate proper response about location search result.
    {shared_prompt}
    <search_result>
    {result}
    </search_result>
"""


class SearchLocationTool(Tool):
    name = "search_location"
    description = "Find places that are not nearby"
    parameters = {
        "type": "object",
        "properties": {
            "text_query": {
                "type": "string",
                "description": 'textQuery in the searchText API of Google Places, e.g., "10 High Street, UK", "ChainRestaurant New York", "Spicy Vegetarian Food in Paris"',
            }
        },
        "required": ["text_query"],
    }

    async def call(self, ctx: Context, text_query: str) -> ToolResult:
        LOG.info(f"Tool called: SearchLocation | text query[{text_query}]", extra=self.log_extra(ctx, keywords=text_query))
        cache = lru_manager.get_db(DBS.LOCATION)
        result = cache.get(text_query)
        if result:
            return result
        result_list = await google_places_client.search_text(text_query)
        if not result_list:
            return self.result(result="No related places found", direct_output=True)

        search_result = "\n".join(
            [
                f'{item.get("displayName", {}).get("text", "")}:{item.get("formattedAddress", "")}'
                for item in result_list
            ]
        )
        # shared_prompt = attach_response_length_prompt(RESPONSE_PROMPT_TEMPLATE, ctx)
        result = self.result(
            result=SEARCH_LOCATION_RESPONSE_PROMPT.format(shared_prompt=RESPONSE_PROMPT_TEMPLATE, result=search_result)
        )
        cache.set(text_query, result)
        return result
