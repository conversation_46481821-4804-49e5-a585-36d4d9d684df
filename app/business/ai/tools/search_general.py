# coding = utf-8
import asyncio
from typing import List

from business.ai.prompts import RESPONSE_PROMPT_TEMPLATE
from business.ai.tools import Tool
from client import searxng_client
from models.common import Context, ToolResult
from models.web_search import ResultItem
from utils.cache.lru import lru_manager, DBS
from utils import logging
from business.ai.prompts import attach_response_length_prompt

LOG = logging.getLogger(__name__)


WEB_SEARCH_RESPONSE_PROMPT = """
    You are Perplexica, an AI model specialized in web search and crafting concise, professional answers but with details.
    
    CRITICAL CONTENT DELIVERY REQUIREMENTS:
    - ALWAYS provide the ACTUAL information from the search results, NOT just the sources
    - NEVER respond with "According to [source]..." without including the actual content
    - NEVER say "You can find this information at..." - PROVIDE the information directly
    - NEVER suggest where to look for information - GIVE the information itself
    - NEVER respond with "The search results show..." without stating what they actually show
    - NEVER mention source URLs or website names unless specifically relevant to the answer
    - <PERSON>XTRACT and <PERSON>UM<PERSON><PERSON>Z<PERSON> the key facts, data, and insights from the search context
    - Focus on answering the user's question with concrete information, not metadata about sources
    - CRITICAL: Maintain the SAME LANGUAGE as the user's original query in your response
    - DO NOT translate the response to a different language
    - CRITICAL: Never mention knowledge limitations, search processes, or tool usage
    - CRITICAL: Present information as if you always knew it - no disclaimers or explanations
    - CRITICAL: Never say "I don't have access to", "I don't know", "I need to search" - just provide the information directly
    
    FORBIDDEN RESPONSE PATTERNS:
    - "According to [website], you can find..."
    - "The information is available at..."  
    - "Based on the search results, there are sources that..."
    - "You can check [source] for more details..."
    - "The search shows various sources mentioning..."
    - "I don't have access to..."
    - "I don't obtain knowledge of..."
    - "I need to search for..."
    - "Let me find that information..."
    - "I'll search the web for..."
    - Any acknowledgment of knowledge limitations
    - Any explanation of search process or tool usage
    
    REQUIRED RESPONSE PATTERN:
    - Lead with direct answers using the actual information found
    - Include specific facts, numbers, dates, and details from the context
    - Synthesize information from multiple sources into a coherent answer
    - Only mention sources if they add credibility to controversial or surprising claims
    - Maintain the original language of the query throughout the response
    - Present information as if you always knew it - no disclaimers or explanations
    
    {shared_prompt}
    
    <context>
    {context}
    </context>
"""

SEARXNG_CATEGORIES = [
    "general",
    "images",
    "videos",
    "news",
    "map",
    "music",
    "it",
    "science",
    "files",
    "social_media",
    "sports"
]


class GeneralSearcherTool(Tool):
    name = "search_general"
    description = "Get answers to questions from the web"
    parameters = {
        "type": "object",
        "properties": {
            "keywords": {"type": "string", "description": "rephrased question"},
            "categories": {
                "type": "array",
                "description": 'categories of data to search',
                "items": {"type": "string", "enum": SEARXNG_CATEGORIES},
                "minItems": 1,
            }
        },
        "required": ["keywords", "categories"],
    }

    async def call(self, ctx: Context, keywords: str, categories: list = ["general"]) -> ToolResult:
        LOG.info(f"Tool called: GeneralSearch | keywords[{keywords}] | categories[{categories}]", extra=self.log_extra(ctx, keywords=keywords))
        if keywords == "not_needed":
            return self.empty_result()
        cache = lru_manager.get_db(DBS.SEARCH)
        result = cache.get(keywords)
        if result:
            return result
    
        if "general" not in categories:
            categories.append("general")
        task = [searxng_client.query(keywords, category=category) for category in categories]
        task_results = await asyncio.gather(*task)
        docs: List[ResultItem] = []
        for result in task_results:
            if result:
                docs.extend(result)
        LOG.info(f"Tool called: GeneralSearch | docs[{len(docs)}]", extra=self.log_extra(ctx, keywords=keywords))
        content = "\n".join([f"{doc.title}\n{doc.content}\n" for doc in docs])
        # shared_prompt = attach_response_length_prompt(RESPONSE_PROMPT_TEMPLATE, ctx)
        result_prompt = WEB_SEARCH_RESPONSE_PROMPT.format(shared_prompt=RESPONSE_PROMPT_TEMPLATE, context=content)
        result = self.result(result_prompt)
        cache.set(keywords, result)
        return result
