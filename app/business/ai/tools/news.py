# coding = utf-8
from typing import List

from business.ai.prompts import RESPONSE_PROMPT_TEMPLATE, attach_response_length_prompt
from business.ai.tools import Tool
from client import newsapi_client
from models.common import Context, ToolResult
from models.web_search import ResultItem
from utils.cache.lru import lru_manager, DBS
from utils import logging

LOG = logging.getLogger(__name__)


class NewsTool(Tool):
    name = "search_news"
    description = "Get recent news articles related to the query"
    parameters = {
        "type": "object",
        "properties": {
            "keywords": {
                "type": "string",
                "description": "keywords for news search",
            }
        },
        "required": ["keywords"],
    }
    response_prompt = """
    You are specialized in news analysis and summarizing news articles.
    {shared_prompt}
    <news_context>
    {context}
    </news_context>
"""

    async def call(self, ctx: Context, keywords: str) -> ToolResult:
        LOG.info(f"Tool called: search_news | keywords[{keywords}]", extra=self.log_extra(ctx, keywords=keywords))
        if keywords == "not_needed":
            return self.empty_result()

        cache = lru_manager.get_db(DBS.NEWS)
        result = cache.get(keywords)
        if result:
            return result

        docs: List[ResultItem] = await newsapi_client.query(keywords)
        content = "\n".join([f"{doc.title}\n{doc.content}\n" for doc in docs])
        # shared_prompt = attach_response_length_prompt(RESPONSE_PROMPT_TEMPLATE, ctx)
        result_prompt = NewsTool.response_prompt.format(shared_prompt=RESPONSE_PROMPT_TEMPLATE, context=content)
        result = self.result(result_prompt)
        cache.set(keywords, result)
        return result
