import asyncio
import datetime
from typing import List
from business.clients.chat_session import get_shared_message_manager, get_shared_session_manager
from business.clients.middlewares import get_shared_redis_client
from config.settings import settings
from models.message import Message, MessageCategory, MessageRole
import utils.logging as logging


class ChattingMemory(object):
    """
    先简单实现一个会话记忆，后续把开源弄进来替换掉
    """

    class Rule:
        def __init__(self):
            self.max_size = settings.MAX_HISTORY_MESSAGES
            self.max_ttl = datetime.timedelta(seconds=settings.MAX_HISTORY_MESSAGE_TTL_SECONDS)
            self.cache_ttl = settings.HISTORY_TTL_SECONDS
            self.use_cache = True

    def __init__(
        self,
        user_id: str,
        session_id: str,
        rule: Rule = None,
        msg_mgr=get_shared_message_manager(),
        sess_mgr=get_shared_session_manager(),
        redis_cli=get_shared_redis_client(),
    ):
        self.user_id = user_id
        self.session_id = session_id
        self._msg_mgr = msg_mgr
        self._sess_mgr = sess_mgr
        self._redis_cli = redis_cli
        self.rule = rule or self.Rule()
        self.log = logging.LoggerAdapter(
            logging.getLogger(__name__),
            extra={
                "session_id": session_id,
                "user_id": user_id,
            },
        )

    async def init(self):
        try:
            await self._sess_mgr.must_get_session(self.session_id, self.user_id)
        except Exception as e:
            self.log.error(f"Failed to initialize session due to {e}", exc_info=True, extra={"err": str(e)})

    async def get(self) -> List[Message]:
        """
        获取历史消息
        """
        rewrite_cache = False
        msg_list = await self._get_from_cache()
        if not msg_list:
            self.log.warning("Getting history messages from cache failed, trying to get from persistent storage")
            rewrite_cache = True
            msg_list = await self._get_from_persistent()

        truncated_msg_list, truncated = await self._truncate_history(msg_list)
        if rewrite_cache or truncated:
            self.log.warning(
                "History message truncated, rewriting cache",
                extra={
                    "before": len(msg_list),
                    "after": len(truncated_msg_list),
                },
            )
            await self._rewrite_cache(truncated_msg_list)

        filtered_msgt_list = self._filter_messages(truncated_msg_list)
        return filtered_msgt_list

    async def add(self, msg: List[Message] | Message, blocking=True):
        """
        添加一条消息
        """
        if isinstance(msg, Message):
            msg = [msg]
        async with self._redis_cli.pipeline(transaction=False) as redis_pipe:
            for m in msg:
                await redis_pipe.lpush(self._cache_key, m.model_dump_json())
                await redis_pipe.expire(self._cache_key, self.rule.cache_ttl)
            cache_result, persistent_result = await asyncio.gather(
                redis_pipe.execute(),
                self._msg_mgr.push_message(msg, blocking),
                return_exceptions=True,
            )
        if isinstance(cache_result, Exception):
            self.log.error(
                "Failed to push message to cache",
                exc_info=True,
                extra={"err": str(cache_result)},
            )
        if isinstance(persistent_result, Exception):
            self.log.error(
                "Failed to push message to persistent storage",
                exc_info=True,
                extra={"err": str(persistent_result)},
            )

    async def _get_from_cache(self):
        """
        从缓存中获取
        """
        if not self.rule.use_cache:
            return []
        try:
            return [
                Message.model_validate_json(x)
                for x in reversed(await self._redis_cli.lrange(self._cache_key, 0, self.rule.max_size))
            ]
        except Exception as e:
            self.log.error("Failed to get messages from cache", exc_info=True, extra={"err": str(e)})
            return []

    async def _rewrite_cache(self, msg_list: List[Message]):
        """
        重写缓存
        """
        try:
            async with self._redis_cli.pipeline() as pipe:
                pipe.delete(self._cache_key)
                for msg in msg_list:
                    pipe.lpush(self._cache_key, msg.model_dump_json())
                pipe.expire(self._cache_key, self.rule.cache_ttl)
                await pipe.execute()
        except Exception as e:
            self.log.error("Failed to rewrite cache", exc_info=True, extra={"err": str(e), "msg_list": msg_list})

    async def _get_from_persistent(self):
        """
        从持久化中获取
        """
        try:
            msgs, _, _ = await self._msg_mgr.search_messages(
                self.user_id,
                session_ids=[self.session_id],
                sorts=[("created_at", -1)],
                page_size=self.rule.max_size,
            )
            if len(msgs) > 0:
                await self._rewrite_cache(msgs)
            return msgs
        except Exception as e:
            self.log.error("Failed to get messages from persistent storage", exc_info=True, extra={"err": str(e)})
            return []

    async def _truncate_history(self, msg_list: List[Message]):
        """
        截断历史消息

        msg_list 为按照时间正序排列的消息列表
        """
        try:
            start_at = datetime.datetime.now(datetime.timezone.utc) - self.rule.max_ttl
            truncated_at = -2

            for i in range(len(msg_list) - 1, -1, -1):
                if datetime.datetime.fromisoformat(msg_list[i].created_at).astimezone(datetime.timezone.utc) < start_at:
                    # 如果消息时间小于最大过期时间，则该 idx 后续的任何消息都不需要了，因为绝对过期
                    truncated_at = i
                    break
            is_truncated = truncated_at != -2
            if is_truncated:
                msg_list = msg_list[truncated_at + 1:]
                msg_list = list(reversed(msg_list))
                while msg_list and msg_list[-1].role != MessageRole.User:
                    msg_list.pop(-1)
                msg_list = list(reversed(msg_list))
                return msg_list, is_truncated
            else:
                return msg_list, False
        except Exception as e:
            self.log.error(
                "Failed to truncate history messages", exc_info=True, extra={"err": str(e), "msg_list": msg_list}
            )
            return msg_list, False

    @property
    def _cache_key(self):
        return f"session:message:{self.session_id}"

    def _filter_messages(self, msg_list: List[Message]):
        remove_round_keys = set()
        funcs = [self._remove_intent_messages]

        try:
            for f in funcs:
                for k in f(msg_list):
                    remove_round_keys.add(k)
        except Exception as e:
            self.log.error(
                "Failed to filter messages",
                exc_info=True,
                extra={
                    "err": str(e),
                    "filter_func": f.__name__,
                },
            )

        return list(filter(lambda x: x.round_key not in remove_round_keys, msg_list))

    def _remove_intent_messages(self, msg_list: List[Message]):
        """
        移除意图消息
        """
        return set([x.round_key for x in filter(lambda x: x.message_category == MessageCategory.Intent, msg_list)])
