from typing import List
from abc import ABC, abstractmethod
from business.ai.prompts.general import get_system_prompt
from models.common import Context
from business.ai.llm import BaseLLMClient
from business.ai.tools import Tool
from utils import logging

LOG = logging.getLogger(__name__)


class AIServiceBackend(ABC):
    """
    所有LLM客户端的基类
    """

    def __init__(self, client: BaseLLMClient, tool: List[Tool], model: str):
        self._client = client
        self._tools = tool
        self._model = model

    @property
    def model(self):
        return self._model

    @property
    @abstractmethod
    def name(self) -> str:
        """必须由子类实现的LLM名称属性"""
        raise NotImplementedError("Subclasses must implement name property")

    async def send_chat_stream(
        self,
        ctx: Context,
        system_prompt: str,
        messages,
        temperature: float = 0.5,
        max_tokens: int = 1000,
        tool_choice: str = "auto",
    ):
        async for chunk in self._client.send_chat_stream(
            ctx,
            self._model,
            system_prompt,
            messages,
            temperature=temperature,
            max_tokens=max_tokens,
            tools=self._tools,
            tool_choice=tool_choice,
        ):
            yield chunk

    def log_extra(self, ctx: Context):
        return {
            "session_id": ctx.session_id,
            "user_id": ctx.user_id,
            "backend_name": self.name,
            "backend_model": self.model,
        }

    async def system_prompt(self, ctx: Context):
        pmt = get_system_prompt(ctx)
        return pmt
