from business.ai.backend.base import AIServiceBackend
from business.ai.llm import Gemini<PERSON><PERSON>A<PERSON>pter, shared_gemini_client


class GeminiBackend(AIServiceBackend):
    name = "gemini"
    def __init__(self, tools, model):
        super().__init__(shared_gemini_client, GeminiToolAdapter(tools=tools), model)


class GEMINI_1_5_PRO(GeminiBackend):
    def __init__(self, tools):
        super().__init__(tools, "gemini-1.5-pro")


class GEMINI_2_5_PRO(GeminiBackend):
    def __init__(self, tools):
        super().__init__(tools, "gemini-2.5-pro-exp-03-25")


class GEMINI_2_0_FLASH_LITE(GeminiBackend):
    def __init__(self, tools):
        super().__init__(tools, "gemini-2.0-flash-lite")


class GEMINI_2_0_FLASH(GeminiBackend):
    def __init__(self, tools):
        super().__init__(tools, "gemini-2.0-flash")
