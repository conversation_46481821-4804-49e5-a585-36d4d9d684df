from business.ai.backend.base import AIServiceBackend
from business.ai.llm import OepnAITool<PERSON>dapter, shared_mistral_client


class MistralBackend(AIServiceBackend):
    name = "mistral"
    def __init__(self, tools, model):
        super().__init__(shared_mistral_client, OepnAIToolAdapter(tools=tools), model)


class MISTRAL_LARGE_LATEST(MistralBackend):
    def __init__(self, tools):
        super().__init__(tools, "mistral-large-latest")


class MISTRAL_SMALL_LATEST(MistralBackend):
    def __init__(self, tools):
        super().__init__(tools, "mistral-small-latest")
