# coding = utf-8
import asyncio
from concurrent.futures import ThreadPoolExecutor
import soundfile as sf
import io
import numpy as np
import time

from config.settings import settings
# from business.ai import faster_whisper_model
from utils import logging

LOG = logging.getLogger(__name__)


class TranscribeService:
    def __init__(self):
        # self.faster_model = faster_whisper_model
        self.executor = ThreadPoolExecutor(max_workers=settings.FASTER_WHISPER_WORKERS)

    @staticmethod
    def _process_audio_bytes(audio_bytes: bytes) -> np.ndarray:
        """处理音频字节数据，返回处理后的numpy数组

        Args:
            audio_bytes: 原始音频字节数据

        Returns:
            处理后的float32类型numpy数组
        """
        # 直接从内存中读取音频数据
        audio_buffer = io.BytesIO(audio_bytes)
        # soundfile 可以直接从 BytesIO 读取音频数据
        audio_data, sample_rate = sf.read(audio_buffer)

        # 如果是立体声，转换为单声道
        if len(audio_data.shape) > 1:
            audio_data = audio_data.mean(axis=1)

        # Whisper 期望输入是 float32 类型的数组
        if audio_data.dtype != np.float32:
            audio_data = audio_data.astype(np.float32)

        return audio_data

    @staticmethod
    def _process_audio_bytes_stream(audio_bytes: bytes) -> np.ndarray:
        """处理音频字节数据，返回处理后的numpy数组

        Args:
            audio_bytes: 原始音频字节数据（PCM格式）

        Returns:
            处理后的float32类型numpy数组
        """
        # 直接将PCM字节数据转换为numpy数组
        audio_data = np.frombuffer(audio_bytes, dtype=np.int16)

        # 将int16转换为float32并归一化到[-1, 1]范围
        audio_data = audio_data.astype(np.float32) / 32768.0

        return audio_data

    async def transcribe_audio(self, audio_bytes: bytes):
        """调用 Whisper 模型进行异步转录"""
        loop = asyncio.get_event_loop()
        process_times = {}

        try:
            # 记录音频处理开始时间
            audio_process_start = time.perf_counter()

            # 处理音频数据
            audio_data = self._process_audio_bytes(audio_bytes)

            process_times["audio_processing"] = time.perf_counter() - audio_process_start

            # 记录模型推理开始时间
            model_start = time.perf_counter()
            result = await loop.run_in_executor(
                self.executor,
                lambda: self.faster_model.transcribe(audio_data, batch_size=settings.FASTER_WHISPER_BATCH_SIZE),
            )

            process_times["model_inference"] = time.perf_counter() - model_start

            LOG.info(
                f"""
Static transcription performance metrics:
- Audio processing: {process_times['audio_processing']:.3f}s
- Model inference: {process_times['model_inference']:.3f}s")
- Result: {result}
"""
            )

            return result
        except Exception as e:
            LOG.error(f"Transcription failed: {str(e)}", exc_info=True)
            raise e

    async def transcirbe_audio_stream(self, client_id: str, audio_chunk: bytes):
        LOG.info(f"Processing audio chunk: {len(audio_chunk)} bytes")
        try:
            # 将字节数据转换为numpy数组
            audio_data = self._process_audio_bytes_stream(audio_chunk)
            t0 = time.perf_counter()
            segments, info = self.faster_model.transcribe(
                audio_data,
                beam_size=5,
                vad_filter=True,  # 启用VAD过滤静音
            )
            t1 = time.perf_counter()
            LOG.info(f"- Model inference: {t1 - t0:.3f}s")
            text = " ".join([seg.text for seg in segments])
            return text
        except Exception as e:
            LOG.error(f"Error processing audio: {str(e)}", exc_info=True)
            raise
