# coding = utf-8

from typing import List

# TODO: 临时方案，后续需要优化
# 多语言扩展性低，维护成本高
# 启动动词
ACTIVATE_VERBS_ENGLISH = [
    "start", "begin", "enable", "activate", "turn on", "launch",
    "open", "initialize", "initiate", "trigger", "power up", "power on",
    "start up", "start off",
]
ACTIVATE_VERBS_CHINESE = [
    "启动", "开启", "打开", "启用", "开始", "使用", "运行",
]
ACTIVATE_VERBS_TRADITIONAL_CHINESE = [
    "啟動", "開啟", "打開", "啟用", "開始",
]

ACTIVATE_VERBS = ACTIVATE_VERBS_ENGLISH + ACTIVATE_VERBS_CHINESE + ACTIVATE_VERBS_TRADITIONAL_CHINESE

def generate_verb_noun_combinations(verbs: List[str], nouns: List[str]) -> List[str]:
    """
    Generate all possible combinations of verbs and nouns.
    Rules for spacing:
    1. English + English: add space (e.g., "start translate")
    2. Chinese + Chinese: no space (e.g., "启动翻译")
    3. English + Chinese: no space (e.g., "start翻译")
    4. Chinese + English: no space (e.g., "启动translate")
    """
    combinations = []
    for verb in verbs:
        for noun in nouns:
            # Check if both verb and noun are English
            is_verb_english = all(c.isascii() for c in verb)
            is_noun_english = all(c.isascii() for c in noun)
            
            if is_verb_english and is_noun_english:
                # English + English: add space
                combinations.append(f"{verb} {noun}")
            else:
                # All other cases: no space
                combinations.append(f"{verb}{noun}")
    return combinations