# coding = utf-8

from .common import ACTIVATE_VERBS, generate_verb_noun_combinations

# TODO: 临时方案，后续需要优化
# 多语言扩展性低，维护成本高
# 翻译名词
TRANSLATE_NOUNS_ENGLISH = [
    "translate", "translation",
]
TRANSLATE_NOUNS_CHINESE = [
    "翻译", "翻译器",
]
TRANSLATE_NOUNS_TRADITIONAL_CHINESE = [
    "翻譯", "翻譯器",
]

TRANSLATE_NOUNS = TRANSLATE_NOUNS_ENGLISH + TRANSLATE_NOUNS_CHINESE + TRANSLATE_NOUNS_TRADITIONAL_CHINESE

def get_translate_intents():
    """
    生成组合翻译
    """
    return generate_verb_noun_combinations(ACTIVATE_VERBS, TRANSLATE_NOUNS)

