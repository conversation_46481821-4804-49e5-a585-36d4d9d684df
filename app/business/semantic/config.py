# coding = utf-8
from typing import List, Optional

import torch
from transformers import ModernBertConfig


class SemanticConfig(ModernBertConfig):
    def __init__(self, tags: List[str], cls_pos_weight: Optional[torch.Tensor] = None, *args, **kwargs):
        self.cls_pos_weight: Optional[torch.Tensor] = cls_pos_weight
        self.tags: List[str] = tags
        super().__init__(*args, **kwargs)


LANGUAGE_MAPPING = {
    "阿拉伯语": "ar-AE",
    "阿拉伯文": "ar-AE",
    "孟加拉语": "bn-IN",
    "孟加拉文": "bn-IN",
    "粤语": "zh-HK",
    "广东话": "zh-HK",
    "香港话": "zh-HK",
    "中文": "zh-CN",
    "捷克语": "cs-CZ",
    "捷克文": "cs-CZ",
    "丹麦语": "da-DK",
    "丹麦文": "da-DK",
    "荷兰语": "nl-NL",
    "荷兰文": "nl-NL",
    "英语": "en-US",
    "英文": "en-US",
    "芬兰语": "fi-FI",
    "芬兰文": "fi-FI",
    "法语": "fr-FR",
    "法文": "fr-FR",
    "德语": "de-DE",
    "德文": "de-DE",
    "印地语": "hi-IN",
    "印地文": "hi-IN",
    "意大利语": "it-IT",
    "意大利文": "it-IT",
    "日语": "ja-JP",
    "日文": "ja-JP",
    "韩语": "ko-KR",
    "韩文": "ko-KR",
    "挪威语": "nb-NO",
    "挪威文": "nb-NO",
    "波兰语": "pl-PL",
    "波兰文": "pl-PL",
    "葡萄牙语": "pt-PT",
    "葡萄牙文": "pt-PT",
    "俄语": "ru-RU",
    "俄文": "ru-RU",
    "西班牙语": "es-ES",
    "西班牙文": "es-ES",
    "瑞典语": "sv-SE",
    "瑞典文": "sv-SE",
    "土耳其语": "tr-TR",
    "土耳其文": "tr-TR",
    "乌克兰语": "uk-UA",
    "乌克兰文": "uk-UA",
    "Arabic".lower(): "ar-AE",
    "Bengali".lower(): "bn-IN",
    "Cantonese".lower(): "zh-HK",
    "Chinese".lower(): "zh-CN",
    "Czech".lower(): "cs-CZ",
    "Danish".lower(): "da-DK",
    "Dutch".lower(): "nl-NL",
    "English".lower(): "en-US",
    "Finnish".lower(): "fi-FI",
    "French".lower(): "fr-FR",
    "German".lower(): "de-DE",
    "Hindi".lower(): "hi-IN",
    "Italian".lower(): "it-IT",
    "Japanese".lower(): "ja-JP",
    "Korean".lower(): "ko-KR",
    "Norwegian".lower(): "nb-NO",
    "Polish".lower(): "pl-PL",
    "Portuguese".lower(): "pt-PT",
    "Russian".lower(): "ru-RU",
    "Spanish".lower(): "es-ES",
    "Swedish".lower(): "sv-SE",
    "Turkish".lower(): "tr-TR",
    "Ukrainian".lower(): "uk-UA"
}
