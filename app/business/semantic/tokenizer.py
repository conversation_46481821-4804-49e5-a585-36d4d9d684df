# coding = utf-8
from typing import Union, Optional, Dict

from transformers import PreTrainedTokenizerFast
from transformers.tokenization_utils_base import EncodedInput, BatchEncoding
from transformers.utils import PaddingStrategy


class SemanticTokenizer(PreTrainedTokenizerFast):
    ignore_label_id = -100

    entity_token_id = 151648
    entity_end_token_id = 151649
    entity_label_token_id = 151650
    entity_label_end_token_id = 151651
    pad_token_id = 151645
    integer_token_id = 151652

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def _pad(
            self,
            encoded_inputs: Union[Dict[str, EncodedInput], BatchEncoding],
            max_length: Optional[int] = None,
            padding_strategy: PaddingStrategy = PaddingStrategy.DO_NOT_PAD,
            pad_to_multiple_of: Optional[int] = None,
            padding_side: Optional[bool] = None,
            return_attention_mask: Optional[bool] = None,
    ):
        res = super()._pad(encoded_inputs, max_length, padding_strategy, pad_to_multiple_of, padding_side, return_attention_mask)
        if "labels" in res:
            empty_label = [SemanticTokenizer.ignore_label_id] * (max_length - len(encoded_inputs["labels"]))
            encoded_inputs["labels"].extend(empty_label)
        return res
