# coding = utf-8
import re

import numpy
import torch


def create_masked_lm_predictions(batch_tokens, special_token_ids, masked_lm_prob, mask_token_id, vocab_size, device, allow_special_token_ids):
    """
    PyTorch 版本的 masking 函数
    """
    special_token_ids = set(special_token_ids)
    masked_tokens = batch_tokens.clone()
    masked_lm_labels = torch.full((len(batch_tokens), len(batch_tokens[0])), -100)

    for row, tokens in enumerate(batch_tokens):
        cand_indexes = []

        for (i, token) in enumerate(tokens):
            if token not in special_token_ids or (token in allow_special_token_ids):
                cand_indexes.append(i)

        numpy.random.shuffle(cand_indexes)  # 仍然可以使用 NumPy 的 shuffle，或者使用 torch.randperm

        num_to_mask = int(len(cand_indexes) * masked_lm_prob)
        masked_lms_positions = cand_indexes[:num_to_mask]

        for index in masked_lms_positions:
            masked_lm_labels[row, index] = tokens[index]
            rand_num = numpy.random.random()
            if rand_num < 0.8:
                masked_tokens[row, index] = mask_token_id
            elif rand_num < 0.9:
                random_index = numpy.random.randint(0, vocab_size)
                masked_tokens[row, index] = random_index
            else:
                pass
    return masked_tokens.to(device=device), masked_lm_labels.to(device=device)


def log_sum_exp(vec):
    batch_size = vec.shape[0]
    max_score = torch.max(vec, 2).values
    max_score_broadcast = max_score.view(batch_size, -1, 1).expand(batch_size, -1, vec.shape[2])
    exp_res = torch.exp(vec - max_score_broadcast)
    return max_score + torch.log(torch.sum(exp_res, dim=2))


def preprocess_item(text):
    return re.sub(r"\d+", "<INTEGER>", text)