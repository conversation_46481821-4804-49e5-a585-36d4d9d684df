# coding = utf-8
import asyncio
import dataclasses
import os.path
import re
import threading
from asyncio import Abstract<PERSON><PERSON><PERSON>oop
from queue import Queue
from typing import List, Optional

import torch
from ray import serve

from business.semantic.config import SemanticConfig, LANGUAGE_MAPPING
from business.semantic.model import <PERSON>man<PERSON><PERSON>ode<PERSON>, OutputResult, InferenceMode
from business.semantic.tokenizer import SemanticTokenizer
from config.settings import settings
from core.ray_helper import DeploymentMixin
from models.intent import IntentCategory
from models.common import Context
from business.ai.tools import validate_glasses_cmd, SEPARATORS_LIST
from utils import logging
from .data import get_translate_intents, get_quicknote_intents

LOG = logging.getLogger(__name__)

SIMPLE_INTENT_MAP = {
    "qn_on": get_quicknote_intents(),
    "transl_on": get_translate_intents()
}

@dataclasses.dataclass
class QueueItem:
    text: str
    loop: AbstractEventLoop = dataclasses.field(default_factory=asyncio.get_running_loop)
    event: asyncio.Event = dataclasses.field(default_factory=asyncio.Event)
    result: Optional[OutputResult] = None
    err: Optional[str] = None
    integer_list: List[str] = dataclasses.field(init=False)

    def __post_init__(self):
        self.integer_list = re.findall(r"\d+", self.text) or []
        self.text = self.text.strip()
        self.text = re.sub(r"\d+", "<INTEGER>", self.text.lower())
        self.text = self.text.strip(SEPARATORS_LIST)

class IntentService:

    def __init__(self):
        self.all_labels = [
            "disp_bright_auto_off",
            "disp_bright_auto_on",
            "disp_bright_dec",
            "disp_bright_inc",
            "disp_bright_max",
            "disp_bright_min",
            "disp_bright_set",
            "disp_dist_dec",
            "disp_dist_inc",
            "disp_dist_max",
            "disp_dist_min",
            "disp_dist_set",
            "disp_ht_dec",
            "disp_ht_inc",
            "disp_ht_max",
            "disp_ht_min",
            "disp_ht_set",
            "disp_on",
            "dp_off",
            "dp_on",
            "navi_on",
            "notify_off",
            "notify_on",
            "qn_on", # 暂时下掉quick note，但是这里不能删掉，删掉会出错
            "silent_on",
            "telep_on",
            "transc_on",
            "transl_on",
        ]
        self.entity_labels = ["dest", "from_lan", "src", "to_lan", "transp", "value"]
        self.ix_to_label = {idx: label for idx, label in enumerate(self.all_labels)}
        model_dir = os.path.join(settings.RESOURCES_DIR, "semantic_model")
        self.tokenizer = SemanticTokenizer.from_pretrained(model_dir)
        self.tokenizer.truncation_side = "left"
        model_config = SemanticConfig(
            hidden_size=768,
            intermediate_size=1152,
            num_hidden_layers=1,
            num_attention_heads=12,
            num_labels=len(self.all_labels),
            problem_type="multi_label_classification",
            reference_compile=False,
            vocab_size=len(self.tokenizer),
            classifier_pooling="cls",
            pad_token_id=self.tokenizer.pad_token_id,
            eos_token_id=self.tokenizer.sep_token_id,
            bos_token_id=self.tokenizer.cls_token_id,
            cls_token_id=self.tokenizer.cls_token_id,
            sep_token_id=self.tokenizer.sep_token_id,
            tags=self.entity_labels,
            classifier_dropout=0.5,
        )
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model = SemanticModel(model_config).to(self.device, dtype=torch.float32).eval()
        self.model.load_state_dict(
            torch.load(os.path.join(model_dir, "model.v10.pth"), map_location=torch.device(self.device)), strict=False
        )
        self.model.to(self.device)
        self.queue = Queue()
        self.inference_thread = threading.Thread(target=self.inference_worker)
        self.inference_thread.start()

    def batch_inference(self, batch_data: List[QueueItem]):
        text_list = [item.text for item in batch_data]
        encoding = self.tokenizer(text_list, padding=True, truncation=True, return_tensors="pt")
        with torch.inference_mode():
            output: Optional[OutputResult] = self.model(
                input_ids=encoding["input_ids"].to(self.model.device),
                mode=InferenceMode.combine_mode(InferenceMode.cls, InferenceMode.ner),
                tokenizer=self.tokenizer,
                attention_mask=encoding["attention_mask"],
            )
            if output is None:
                raise ValueError("parse data error")
            scores = torch.sigmoid(output.logits)
            cls_idx_list = torch.argmax(scores, dim=-1)
            score_list = scores[torch.arange(scores.shape[0]), cls_idx_list].tolist()
            cls_idx_list = cls_idx_list.tolist()
            ner_result_list = output.ner_result
        for item, score, cls_idx, ner_result in zip(batch_data, score_list, cls_idx_list, ner_result_list):
            entity_list = []
            for entity in ner_result:
                entity_text = self.tokenizer.decode(entity["ids"]["token_ids"])
                for idx in entity["ids"]["integer_position"]:
                    entity_text = entity_text.replace("<INTEGER>", item.integer_list[idx], 1).strip()
                if entity["label"] in {"from_lan", "to_lan"}:
                    entity_text = LANGUAGE_MAPPING.get(entity_text.lower().strip())
                entity_list.append({"key": entity["label"], "value": entity_text})

            item.result = {
                "intent": self.all_labels[cls_idx],
                "score": score,
                "entities": {entity["key"]: entity["value"] for entity in entity_list},
            }

    def inference_consumer(self):
        while True:
            batch_data: List[QueueItem] = [self.queue.get()]
            while not self.queue.empty() and len(batch_data) < settings.SEMANTIC_BATCH_SIZE:
                batch_data.append(self.queue.get())
            try:
                self.batch_inference(batch_data)
            except Exception as e:
                for item in batch_data:
                    item.err = str(e)
            finally:
                for item in batch_data:
                    item.loop.call_soon_threadsafe(item.event.set)

    def inference_worker(self):
        cuda_stream = None
        if torch.cuda.is_available():
            cuda_stream = torch.cuda.Stream()
        with torch.inference_mode():
            if cuda_stream:
                with torch.cuda.stream(cuda_stream):
                    self.inference_consumer()
            else:
                self.inference_consumer()

    async def recognize_intent(self, ctx: Context, text: str):
        """
        识别用户输入的意图
        """

        intent_data = None
        # 首先检查是否匹配 SIMPLE_INTENT_MAP 中的任何模式
        clean_text = text.lower().strip().strip(SEPARATORS_LIST)

        # 第一层：简单确定性规则
        # TODO: 需要优化，避免大的if else
        for intent_name, intent_samples in SIMPLE_INTENT_MAP.items():
            for sample in intent_samples:
                if intent_name == IntentCategory.QuickNote_On:
                    if clean_text.startswith(sample.lower()):
                        intent_data = {
                            "intent": intent_name,
                            "score": 1.0,
                            "entities": None
                        }
                        intent_data["entities"] = {"note": clean_text[len(sample) :].strip()}
                        break
                elif intent_name == IntentCategory.Translate_On:
                    if clean_text == sample:
                        intent_data = {
                            "intent": intent_name,
                            "score": 1.0,
                            "entities": None
                        }
                        break
            if intent_data:
                break

        # 第二层：如果简单匹配没有结果，使用意图识别服务
        if not intent_data:
            item = QueueItem(text=text)
            self.queue.put_nowait(item)
            await item.event.wait()
            intent_data = item.result

        validated_intent_data = validate_glasses_cmd(intent_data)
        '''
        LOG.info(
            f"[NLP INTENT] validate_glasses_cmd: before {intent_data}, after {validated_intent_data}",
            extra={
                "session_id": ctx.session_id,
                "user_id": ctx.user_id,
            }
        )
        '''
        return validated_intent_data

@serve.deployment(max_ongoing_requests=settings.MAX_ONGOING_REQUESTS)
class SemanticIntentDeployment(DeploymentMixin):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._intent_service = IntentService()

    async def recognize_intent(self, ctx: Context, text: str):
        return await self._intent_service.recognize_intent(ctx, text)
