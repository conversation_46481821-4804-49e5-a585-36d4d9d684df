# coding = utf-8
import dataclasses
import enum
from contextlib import nullcontext
from typing import Optional, List, Union, Tuple, Dict

import torch
from torch import nn
from torch.nn import CrossEntropyLoss, BCEWithLogitsLoss
from transformers import PreTrainedModel, ModernBertModel
from transformers.modeling_outputs import SequenceClassifierOutput
from transformers.models.modernbert.modeling_modernbert import ModernBertPredictionHead, _pad_modernbert_output

from business.semantic.config import SemanticConfig
from business.semantic.global_pointer import EfficientGlobalPointer
from business.semantic.tokenizer import SemanticTokenizer
from business.semantic.utils import create_masked_lm_predictions


@dataclasses.dataclass
class OutputResult(SequenceClassifierOutput):
    ner_result: Optional[List[List[int]]] = None
    ner_logits: Optional[torch.Tensor] = None
    cls_loss: Optional[torch.Tensor] = None
    ner_loss: Optional[torch.Tensor] = None
    mlm_loss: Optional[torch.Tensor] = None
    mlm_labels: Optional[torch.Tensor] = None
    mlm_logits: Optional[torch.Tensor] = None


class InferenceMode(enum.Enum):
    cls = 0
    cls_loss = 1
    ner = 2
    ner_loss = 3
    mlm = 4
    mlm_loss = 5

    @staticmethod
    def has(val: int, mode: "InferenceMode"):
        return (1 << mode.value) & val

    @staticmethod
    def combine_mode(*args: "InferenceMode"):
        result = 0
        modes: List["InferenceMode"] = list(args)
        if InferenceMode.cls_loss in modes:
            modes.append(InferenceMode.cls)
        if InferenceMode.ner_loss in modes:
            modes.append(InferenceMode.ner)
        if InferenceMode.mlm_loss in modes:
            modes.append(InferenceMode.mlm)
        for mode in modes:
            result |= 1 << mode.value
        return result


class SemanticModel(PreTrainedModel):
    def __init__(self, config: SemanticConfig, *inputs, **kwargs) -> None:
        super().__init__(config, *inputs, **kwargs)
        self.config = config
        self.num_labels = config.num_labels
        self.modern_bert = ModernBertModel(config)
        self.cls_head = ModernBertPredictionHead(config)
        self.ner_head = ModernBertPredictionHead(config)
        self.mlm_head = ModernBertPredictionHead(config)
        self.drop = torch.nn.Dropout(config.classifier_dropout)
        self.classifier = nn.Linear(config.hidden_size, config.num_labels)
        self.decoder = nn.Linear(config.hidden_size, config.vocab_size, bias=config.decoder_bias)
        self.tag_to_ix = {tag: i for i, tag in enumerate(config.tags)}
        self.ix_to_tag = {i: tag for i, tag in enumerate(config.tags)}
        self.efficient_global_pointer = EfficientGlobalPointer(config)
        self.sparse_prediction = config.sparse_prediction
        self.sparse_pred_ignore_index = config.sparse_pred_ignore_index
        self.post_init()
        if self.config.problem_type == "single_label_classification":
            self.cls_loss_fct = CrossEntropyLoss(label_smoothing=.1)
        elif self.config.problem_type == "multi_label_classification":
            self.cls_loss_fct = BCEWithLogitsLoss(pos_weight=config.cls_pos_weight)

    def conv_label(self, intents: List[str], label_to_ix: Dict[str, int]):
        if self.config.problem_type == "multi_label_classification":
            cls_labels = torch.zeros((len(intents), self.config.num_labels), device=self.device, dtype=self.dtype)
            cls_labels_idx = [[row, (label_to_ix[label] if label in label_to_ix else -1)] for row, label in enumerate(intents)]
            cls_labels_idx = torch.tensor([[row, label_ix] for row, label_ix in cls_labels_idx if label_ix != -1], dtype=torch.long)
            if cls_labels_idx.shape[0] > 0:
                cls_labels[cls_labels_idx[:, 0], cls_labels_idx[:, 1]] = 1.
        else:
            cls_labels = torch.tensor([label_to_ix[row] for row in intents], dtype=torch.long, device=self.device)
        return cls_labels

    def forward(
            self,
            input_ids: Optional[torch.Tensor] = None,
            attention_mask: Optional[torch.Tensor] = None,
            sliding_window_mask: Optional[torch.Tensor] = None,
            position_ids: Optional[torch.Tensor] = None,
            inputs_embeds: Optional[torch.Tensor] = None,
            cls_labels: Optional[torch.Tensor] = None,
            indices: Optional[torch.Tensor] = None,
            cu_seqlens: Optional[torch.Tensor] = None,
            max_seqlen: Optional[int] = None,
            batch_size: Optional[int] = None,
            seq_len: Optional[int] = None,
            output_attentions: Optional[bool] = None,
            output_hidden_states: Optional[bool] = None,
            return_dict: Optional[bool] = None,
            mode: int = 0,
            tokenizer: SemanticTokenizer = None,
            masked_lm_prob: float = 0.,
            ner_labels: Optional[torch.Tensor] = None,
            parse_ner: bool = True,
            **kwargs,
    ) -> Union[Tuple[torch.Tensor], SequenceClassifierOutput]:
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict
        inferen_args = dict(
            attention_mask=attention_mask,
            sliding_window_mask=sliding_window_mask,
            position_ids=position_ids,
            inputs_embeds=inputs_embeds,
            indices=indices,
            cu_seqlens=cu_seqlens,
            max_seqlen=max_seqlen,
            batch_size=batch_size,
            seq_len=seq_len,
            output_attentions=output_attentions,
            output_hidden_states=output_hidden_states,
            return_dict=return_dict,
        )
        cls_logits, cls_loss, ner_loss, mlm_loss, mlm_logits, ner_logits = [None] * 6
        ner_result, mlm_labels = [None] * 2
        hidden_states, attentions = [None] * 2
        if InferenceMode.has(mode, InferenceMode.cls) or InferenceMode.has(mode, InferenceMode.ner):
            outputs = self.modern_bert(
                input_ids=input_ids,
                **inferen_args
            )
            last_hidden_state = outputs[0]

            if InferenceMode.has(mode, InferenceMode.cls):
                # 分类头
                cls_logits = self.classification(last_hidden_state, attention_mask, mode)
                if cls_labels is not None and InferenceMode.has(mode, InferenceMode.cls_loss):
                    cls_loss = self.classification_loss(cls_logits, cls_labels)

            if InferenceMode.has(mode, InferenceMode.ner):
                # 实体抽取头
                ner_logits = self.ner(last_hidden_state, attention_mask)
                if parse_ner:
                    ner_result = self.parse_ner_result(input_ids, ner_logits)
                if InferenceMode.has(mode, InferenceMode.ner_loss):
                    ner_loss = self.ner_loss(ner_logits, ner_labels)
            hidden_states, attentions = outputs.hidden_states, outputs.attentions
        if InferenceMode.has(mode, InferenceMode.mlm):
            # mlm头
            mlm_input_ids, mlm_labels = create_masked_lm_predictions(input_ids,
                                                                     special_token_ids=tokenizer.all_special_ids,
                                                                     masked_lm_prob=masked_lm_prob,
                                                                     mask_token_id=tokenizer.mask_token_id,
                                                                     vocab_size=len(tokenizer),
                                                                     device=self.device,
                                                                     allow_special_token_ids={SemanticTokenizer.integer_token_id})
            outputs = self.modern_bert(
                input_ids=mlm_input_ids,
                **inferen_args
            )
            last_hidden_state = outputs[0]
            mlm_logits = self.mlm(last_hidden_state, mlm_labels, indices, seq_len, batch_size)
            if InferenceMode.has(mode, InferenceMode.mlm_loss):
                mlm_loss = self.mlm_loss(mlm_logits, mlm_labels)
            hidden_states, attentions = outputs.hidden_states, outputs.attentions

        return OutputResult(
            cls_loss=cls_loss,
            ner_loss=ner_loss,
            mlm_loss=mlm_loss,
            logits=cls_logits,
            hidden_states=hidden_states,
            attentions=attentions,
            ner_result=ner_result,
            ner_logits=ner_logits,
            mlm_labels=mlm_labels,
            mlm_logits=mlm_logits,
        )

    def parse_ner_result(self, input_ids, ner_logits):  # [btz, 1, 1, seq_len]
        decode_input_ids = input_ids.tolist()
        result: List[List[Dict]] = []
        for _ in range(len(decode_input_ids)):
            result.append([])
        ner_score_cpu = torch.sigmoid(ner_logits).cpu()
        result_info = torch.where(ner_logits > 0)
        for idx in range(result_info[0].shape[0]):
            row, tag_id, start, end = [int(v[idx]) for v in result_info]
            tok_ids = decode_input_ids[row][start: end + 1]
            int_cnt = tok_ids.count(SemanticTokenizer.integer_token_id)
            int_start = decode_input_ids[row][: start].count(SemanticTokenizer.integer_token_id)
            if int_cnt > 0:
                integer_position = [i for i in range(int_start, int_start + int_cnt)]
            else:
                integer_position = []
            result[row].append({"label": self.ix_to_tag[tag_id],
                                "ids": {
                                    "token_ids": decode_input_ids[row][start: end + 1],
                                    "integer_position": integer_position
                                },
                                "score": ner_score_cpu[row, tag_id, start, end]})
        return result

    def classification(self, last_hidden_state, attention_mask, mode):
        cls_head = self.cls_head(last_hidden_state)
        if InferenceMode.has(mode, InferenceMode.cls_loss):
            cls_head = self.drop(cls_head)
        if self.config.classifier_pooling == "cls":
            cls_head = cls_head[:, 0]
        elif self.config.classifier_pooling == "mean":
            cls_head = (cls_head * attention_mask.unsqueeze(-1)).sum(dim=1) / attention_mask.sum(
                dim=1, keepdim=True
            )
        cls_logits = self.classifier(cls_head)
        return cls_logits

    def classification_loss(self, logits, label):
        loss = None
        if self.config.problem_type is None:
            if self.num_labels == 1:
                self.config.problem_type = "regression"
            elif self.num_labels > 1 and (label.dtype == torch.long or label.dtype == torch.int):
                self.config.problem_type = "single_label_classification"
            else:
                self.config.problem_type = "multi_label_classification"

        if self.config.problem_type == "single_label_classification":
            loss = self.cls_loss_fct(logits.view(-1, self.num_labels), label.view(-1))
        elif self.config.problem_type == "multi_label_classification":
            loss = self.cls_loss_fct(logits, label)
        return loss

    def ner(self, last_hidden_state, ner_mask: torch.Tensor):
        ner_head = self.ner_head(last_hidden_state)
        ner_head = self.drop(ner_head)
        ner_logits = self.efficient_global_pointer(ner_head, ner_mask)
        return ner_logits

    def ner_loss(self, ner_logits, ner_labels):
        y_true = ner_labels.view(ner_labels.shape[0] * ner_labels.shape[1], -1)  # [btz*ner_vocab_size, seq_len*seq_len]
        y_pred = ner_logits.reshape(ner_logits.shape[0] * ner_logits.shape[1], -1)  # [btz*ner_vocab_size, seq_len*seq_len]

        y_pred = (1 - 2 * y_true) * y_pred
        y_pred_pos = y_pred - (1 - y_true) * 1e12
        y_pred_neg = y_pred - y_true * 1e12

        y_pred_pos = torch.cat([y_pred_pos, torch.zeros_like(y_pred_pos[..., :1])], dim=-1)
        y_pred_neg = torch.cat([y_pred_neg, torch.zeros_like(y_pred_neg[..., :1])], dim=-1)
        pos_loss = torch.logsumexp(y_pred_pos, dim=-1)
        neg_loss = torch.logsumexp(y_pred_neg, dim=-1)
        return (pos_loss + neg_loss).mean()

    def mlm(self, last_hidden_state, labels, indices, seq_len, batch_size):
        if self.sparse_prediction and labels is not None:
            # flatten labels and output first
            labels = labels.view(-1)
            last_hidden_state = last_hidden_state.view(labels.shape[0], -1)

            # then filter out the non-masked tokens
            mask_tokens = labels != self.sparse_pred_ignore_index
            last_hidden_state = last_hidden_state[mask_tokens]
            labels = labels[mask_tokens]

        logits = self.decoder(self.mlm_head(last_hidden_state))
        if self.config._attn_implementation == "flash_attention_2":
            with nullcontext() if self.config.repad_logits_with_grad or labels is None else torch.no_grad():
                logits = _pad_modernbert_output(inputs=logits, indices=indices, batch=batch_size, seqlen=seq_len)
        return logits

    def mlm_loss(self, logits, labels):
        loss = self.loss_function(logits, labels, vocab_size=self.config.vocab_size)
        return loss
