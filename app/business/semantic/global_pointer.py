# coding = utf-8

from typing import Optional

import torch
import torch.nn as nn
from transformers.models.modernbert.modeling_modernbert import ModernBertRotaryEmbedding, apply_rotary_pos_emb

from business.semantic.config import SemanticConfig


class EfficientGlobalPointer(nn.Module):
    def __init__(self, config: SemanticConfig):
        super().__init__()
        self.config = config
        self.num_classes = len(config.tags)
        self.head_dim = config.hidden_size // config.num_attention_heads

        self.rotary_emb = ModernBertRotaryEmbedding(config=config, dim=self.head_dim, base=config.global_rope_theta)

        self.p_dense = nn.Linear(config.hidden_size, self.head_dim * 2)
        self.q_dense = nn.Linear(self.head_dim * 2, self.num_classes * 2)

    def forward(self, hidden_states, mask: Optional[torch.tensor] = None, labels: Optional[torch.Tensor] = None):
        batch_size, seq_len = hidden_states.size()[:2]

        # 生成query和key
        sequence_output = self.p_dense(hidden_states)  # [..., head_size*2]
        query, key = sequence_output[..., : self.head_dim], sequence_output[..., self.head_dim:]  # [..., head_size]
        cos, sin = self.rotary_emb(hidden_states, position_ids=torch.arange(seq_len, device=hidden_states.device).unsqueeze(0))
        query, key = apply_rotary_pos_emb(query.unsqueeze(1), key.unsqueeze(1), cos, sin)
        scale = self.head_dim ** -0.5
        logits = torch.einsum('bhmd,bhnd->bhmn', query, key) * scale
        bias_input = self.q_dense(sequence_output)
        bias = torch.stack(torch.chunk(bias_input, self.num_classes, dim=-1), dim=-2).transpose(1, 2) / 2
        logits = logits + bias[..., :1] + bias[..., 1:].transpose(2, 3)
        if mask is not None:
            attention_mask1 = 1 - mask.unsqueeze(1).unsqueeze(3)  # [btz, 1, seq_len, 1]
            attention_mask2 = 1 - mask.unsqueeze(1).unsqueeze(2)  # [btz, 1, 1, seq_len]
            logits = logits.masked_fill(attention_mask1.bool(), value=-float('inf'))
            logits = logits.masked_fill(attention_mask2.bool(), value=-float('inf'))
        logits = logits - torch.tril(torch.ones_like(logits), -1) * 1e12
        return logits


