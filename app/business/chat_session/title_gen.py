from business.ai.ai_agent import AIAgentService
from config.settings import settings, TitleGenRule
from models.intent import IntentCategory
from models.common import ResponseType
from models.session import SessionInfo, SessionTitleExtra
from models.message import Message, MessageRole
from business.ai.prompts import get_summary_prompt
import ujson
from utils import logging

LOG = logging.getLogger(__name__)


async def _gen_by_intent(session: SessionInfo, intentMessage: Message):
    intent_msg = ujson.loads(intentMessage.content)
    intent = intent_msg.get("intent")
    entities = intent_msg.get("entities")
    if intent not in IntentCategory.values():
        LOG.error(f"Intent {intent} not in IntentCategory")
        return None
    target, operator = IntentCategory.get_entity_and_operator(intent)

    session.title_extra = SessionTitleExtra(
        entity=target.value,
        operator=operator.value,
        values=entities,
    )
    # 这里貌似没有翻译的必要了，直接返回一个字符串，终端使用 title_extra 进行渲染
    return f"Intent: {target.value} {operator.value} {entities}"


async def _gen_by_first_question(ctx, session: SessionInfo, history_messages):
    intents = list(filter(lambda x: x.role == MessageRole.CustomIntent, history_messages))
    user_msgs = list(filter(lambda x: x.role == MessageRole.User, history_messages))
    if len(intents) > 0:
        return await _gen_by_intent(session, intents[-1])
    if len(user_msgs) > 0:
        return user_msgs[0].content
    return None

# # TODO: 应该直接使用client
# async def _gen_by_summary(ctx, session: SessionInfo, history_messages):
#     full = []
#     async for resp in ai_agent.process_stream(
#         ctx,
#         get_summary_prompt({"language": ctx.language}),
#     ):
#         if resp.type == ResponseType.Text:
#             full.append(resp.content)

#     full = "".join(full)
#     return full


async def update_session_title(ctx, session: SessionInfo, sess_mgr, mess_mgr):
    # TODO: 暂时注释掉
    # GENERATOR = {
    #     TitleGenRule.FIRST_QUESTION: _gen_by_first_question,
    #     # TitleGenRule.SUMMARY: _gen_by_summary,
    # }
    history_msg = await mess_mgr.list_all_messages(
        ctx.user_id,
        session.session_id,
        roles=[
            MessageRole.CustomIntent.value,
            MessageRole.User.value,
        ],
    )
    # session.title = await GENERATOR[settings.SESSION_TITLE_GEN_RULE](ctx, session, history_msg)
    session.title = await _gen_by_first_question(ctx, session, history_msg)
    await sess_mgr.update_session(session)
    #LOG.info(f"Update Session[{session.session_id}] title -> {session.title}, title_extra -> {session.title_extra}")
