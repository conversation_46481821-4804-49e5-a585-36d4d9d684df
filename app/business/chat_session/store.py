# coding: utf-8
"""
暂时不做过度封装了
"""
import asyncio
import contextlib
import time
from functools import lru_cache
import ujson
from bson import ObjectId
from config.settings import settings
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple, Union
from motor.motor_asyncio import AsyncIOMotorClient
import redis.asyncio as redis

from core import metrics
from models.common import PyObjectId
from models.message import (
    APIChatMessageEntity,
    APIChatMessageQuestionOrAnswer,
    ChatMessageQuestionOrAnswerType,
    Message,
    MessageAttr,
    MessageCategory,
    MessageRole,
)
from models.session import SessionInfo

from utils import logging

LOG = logging.getLogger(__name__)


class SessionManager:
    ANONYMOUS_USER_ID = "Anonymous"

    def __init__(self, mongoClient: AsyncIOMotorClient):
        self.client = mongoClient
        self.db = self.client[settings.MONGO_DB_NAME]
        self._col_sessions = self.db["jarvis_chat_session"]

    async def must_get_session(self, session_id: str, user_id: str) -> Dict:
        """
        获取或创建会话

        这里不使用 findAndModify, 防止 collection 膨胀后性能剧降

        @param session_id: 会话ID
        @param user_id: 用户ID, 如果为空则默认为 Anonymous, 该用户仅限于调适
        """
        session = await self._col_sessions.find_one({"session_id": session_id, "user_id": user_id})
        if not session:
            current_time = datetime.now(timezone.utc).isoformat()
            new_session = {
                "session_id": session_id,
                "user_id": user_id,
                "title": "",
                "title_extra": None,
                "created_at": current_time,
                "updated_at": current_time,
            }
            await self._col_sessions.insert_one(new_session)
            return SessionInfo(**new_session)
        return SessionInfo(**session)

    async def update_session(self, session: SessionInfo):
        """
        更新会话
        """
        current_time = datetime.now(timezone.utc).isoformat()
        session.updated_at = current_time
        await self._col_sessions.update_one(
            {"session_id": session.session_id, "user_id": session.user_id},
            {"$set": session.model_dump(exclude={"id", "created_at"}, mode="json")},
        )

    async def search_session(
        self, user_id, title=None, title_like=None, page_token=None, page_size=20, sorts=None, acquire_count=False
    ):
        """
        搜索会话
        """
        query = {}
        query = {"user_id": user_id}
        if title:
            query["title"] = title
        elif title_like:
            query["title"] = {"$regex": title_like}

        if page_token:
            query["_id"] = {"$gt": ObjectId(page_token)}

        LOG.info(f"search_session query: {query}")
        qs = self._col_sessions.find(query)
        if sorts:
            qs = qs.sort(sorts)

        cnt = None
        if acquire_count:
            cnt = await qs.count()

        return [SessionInfo(**x) for x in await qs.limit(page_size).to_list()], cnt

    async def delete_session(self, user_id, session_ids=[]):
        """
        删除会话
        """
        qs = {"user_id": user_id}
        if session_ids:
            qs["session_id"] = {"$in": [x for x in session_ids]}
        await self._col_sessions.delete_many(qs)


class MessageManager:
    def __init__(self, mongoClient: AsyncIOMotorClient, redisCli: redis.Redis):
        db = mongoClient[settings.MONGO_DB_NAME]
        self._col_messages = db["jarvis_chat_message"]
        self._redis = redisCli
        self._async_pool_task = None
        self._async_pool_queue_monitor_task = None
        self._async_pool_queue = asyncio.Queue()
        self._last_write_time = time.time()
        self._metrics = metrics.MsgMgrMetric()

    async def push_message(self, msg: List[Message] | Message, blocking=True):
        if isinstance(msg, Message):
            msg = [msg]
        body = [m.model_dump(mode="json", exclude={"id"}) for m in msg]
        if blocking:
            await self._col_messages.insert_many(body, ordered=True)
        else:
            if not self._async_pool_task:
                self._async_pool_task = asyncio.create_task(self.push_task())
                self._async_pool_queue_monitor_task = asyncio.create_task(self.push_queue_monitor())
            self._async_pool_queue.put_nowait(body)

    async def push_queue_monitor(self):
        while True:
            qsize = self._async_pool_queue.qsize()
            metrics.MSG_MGR_QSIZE_GAUGE.set(self._metrics, qsize)
            if qsize == 0:
                metrics.MSG_MGR_DELAY_GAUGE.set(self._metrics, 0)
            else:
                metrics.MSG_MGR_DELAY_GAUGE.set(self._metrics, time.time() - self._last_write_time)
            await asyncio.sleep(1)


    async def push_task(self):
        while True:
            body = await self._async_pool_queue.get()
            while True:
                try:
                    chunk = self._async_pool_queue.get_nowait()
                    body.extend(chunk)
                except asyncio.QueueEmpty:
                    break
                except Exception as e:
                    LOG.error(f"push_task get queue err:{e}")
            metrics.MSG_MGR_WRITE_SIZE_GAUGE.set(self._metrics, len(body))
            with contextlib.suppress(Exception):
                await self._col_messages.insert_many(body, ordered=True)
            metrics.MSG_MGR_WRITE_SIZE_GAUGE.set(self._metrics, 0)
            self._last_write_time = time.time()


    async def get_latest_messages(self, session_id: str, limit: int = settings.MAX_HISTORY_MESSAGES) -> List[Message]:
        msgs = [
            Message(**x)
            for x in await self._col_messages.find(
                {
                    "session_id": session_id,
                    "role": {"$in": [MessageRole.display_roles(True)]},
                }
            )
            .sort("created_at", -1)
            .to_list(limit)
        ]
        async with self._redis.pipeline() as pipe:
            rk = self._session_messages_key(session_id)
            pipe.delete(rk)
            for msg in msgs:
                pipe.lpush(rk, msg.model_dump_json())
            await pipe.execute()
        return msgs

    def get_search_message_filter(
        self,
        user_id,
        session_ids=None,
        message=None,
        message_like=None,
        roles=MessageRole.display_roles(True),
    ):

        query = {"user_id": user_id, "role": {"$in": roles}}
        if session_ids:
            query["session_id"] = {"$in": session_ids}

        if message:
            query["content"] = message
        elif message_like:
            query["content"] = {"$regex": message_like}
        return query

    async def search_messages(
        self,
        user_id,
        session_ids=None,
        message=None,
        message_like=None,
        prev_messages=None,
        page_token=None,
        page_size=20,
        sorts=None,
        acquire_count=False,
        roles=MessageRole.display_roles(True),
    ):
        """
        搜索消息
        """
        query = self.get_search_message_filter(
            user_id,
            session_ids,
            message,
            message_like,
            roles=roles,
        )

        page_token_desc = True
        if sorts:
            kv = {s[0]: s[1] for s in sorts}
            if "created_at" in kv:
                page_token_desc = True if kv.get("created_at") == -1 else False

        if page_token:
            query["_id"] = {"$lt" if page_token_desc else "$gt": ObjectId(page_token)}

        qs = self._col_messages.find(query)
        if sorts:
            qs = qs.sort(sorts)

        cnt = None
        if acquire_count:
            cnt = await qs.count()

        results = await qs.limit(page_size).to_list()
        prev_message_list = []

        if prev_messages:
            query["_id"] = {"$gte" if page_token_desc else "$lte": ObjectId(page_token)}
            qs = self._col_messages.find(query)
            if sorts:
                qs.sort(sorts)
            prev_message_list = await qs.limit(prev_messages).to_list()
        return [Message(**x) for x in prev_message_list + results], cnt, -1 if page_token_desc else 0

    async def _api_message_build(self, user_id, msg_list):
        unique_round_key = set([x.round_key for x in msg_list])
        # 搜索出所有完整会话
        round_key_map = {}
        async for row in self._col_messages.find({"user_id": user_id, "round_key": {"$in": list(unique_round_key)}}):
            round_key_map.setdefault(row["round_key"], []).append(Message(**row))
        # 基于原来的顺序构造消息组
        processed_round_keys = set()
        grouped_messages = []
        for m in msg_list:
            if m.round_key in processed_round_keys:
                continue
            grouped_messages.append(round_key_map[m.round_key])
            processed_round_keys.add(m.round_key)
        # 将组封装成 API 需要的格式
        result = []
        for row in grouped_messages:
            parsed = self._build_api_messages(row)
            if parsed:
                result.append(parsed)
        return result

    async def api_search_messages(
        self,
        user_id,
        session_ids=None,
        message=None,
        message_like=None,
        prev_messages=None,
        page_token=None,
        page_size=20,
        sorts=None,
        acquire_count=False,
        roles=MessageRole.display_roles(True),
    ) -> Tuple[List[APIChatMessageEntity], int, PyObjectId]:
        """
        提供给 API 使用的消息搜索
        整个逻辑 & 响应结果都不一样
        """
        msg_list, _, page_token_idx = await self.search_messages(
            user_id,
            session_ids,
            message,
            message_like,
            prev_messages,
            page_token,
            page_size,
            sorts,
            False,
            roles,
        )
        result = await self._api_message_build(user_id, msg_list)

        cnt = 0
        if acquire_count:
            # 如果需要统计总数, 则直接基于过滤条件
            query = self.get_search_message_filter(
                user_id,
                session_ids,
                message,
                message_like,
                roles=roles,
            )
            pipeline = [
                {"$match": query},  # 筛选条件
                {"$group": {"_id": "$round_key"}},  # 按 round_key 分组并统计数量
                {"$count": "count"},  # 统计总数
            ]
            count_result = await self._col_messages.aggregate(pipeline).to_list(1)
            cnt = count_result[0]["count"] if count_result else 0

        next_page_token = None
        if page_token_idx == 0:
            next_page_token = result[0].answer.id if result else None
        else:
            next_page_token = result[-1].question.id if result else None

        return result, cnt, next_page_token

    def _build_api_messages(self, message_list) -> Optional[APIChatMessageEntity]:
        """
        将多个消息合并成 API 需要的消息格式
        """
        question, answer = None, None
        for m in message_list:
            if m.role == MessageRole.User:
                question = m
            elif m.role == MessageRole.Assistant:
                answer = m
        if not question or not answer:
            # 无法成对，基本有问题，就跳过了
            return None

        return APIChatMessageEntity(
            session_id=question.session_id,
            user_id=question.user_id,
            question=self._transform_message(question),
            answer=self._transform_message(answer),
            attrs=answer.attrs,
        )

    @lru_cache
    @staticmethod
    def _get_message_transformer_map():
        return {
            MessageCategory.Text: MessageManager._transform_text_message,
            MessageCategory.Intent: MessageManager._transform_intent_message,
        }

    @staticmethod
    def _transform_message(m: Message) -> APIChatMessageQuestionOrAnswer:
        transformer = MessageManager._get_message_transformer_map().get(
            m.message_category, MessageManager._transform_text_message
        )
        return transformer(m)

    @staticmethod
    def _transform_text_message(m: Message) -> APIChatMessageQuestionOrAnswer:
        return APIChatMessageQuestionOrAnswer(
            id=m.id,
            type=ChatMessageQuestionOrAnswerType.Text,
            content=m.content,
            created_at=m.created_at,
        )

    @staticmethod
    def _transform_intent_message(m: Message) -> APIChatMessageQuestionOrAnswer:
        body = ujson.loads(m.content)
        return APIChatMessageQuestionOrAnswer(
            id=m.id,
            type=ChatMessageQuestionOrAnswerType.Intent,
            intent_category=body.get("intent"),
            intent_entities=body.get("entities"),
            intent_score=body.get("score"),
            created_at=m.created_at,
        )

    async def list_all_messages(self, user_id: str, session_id: str, roles=MessageRole.display_roles(True)):
        """
        获取所有消息
        """
        query = {
            "user_id": user_id,
            "session_id": session_id,
            "role": {"$in": roles},
        }
        return [Message(**x) for x in await self._col_messages.find(query).to_list()]

    async def api_list_all_messages(
        self, user_id: str, session_id: str, roles=MessageRole.display_roles(True)
    ) -> List[APIChatMessageEntity]:
        """
        获取所有消息
        """
        return await self._api_message_build(user_id, await self.list_all_messages(user_id, session_id, roles))

    def _session_messages_key(self, session_id: str):
        return f"session:message:{session_id}"

    async def get_message(self, user_id, msg_id):
        """
        获取消息
        """
        msg = await self._col_messages.find_one({"_id": ObjectId(msg_id), "user_id": user_id})
        if msg:
            return Message(**msg)
        return None

    async def delete_all_messages(self, user_id, session_ids=None):
        """
        删除 session_id 下的所有消息
        """
        qs = {"user_id": user_id}
        if session_ids:
            qs["session_id"] = {"$in": [x for x in session_ids]}
        await self._col_messages.delete_many(qs)

    async def delete_messages(self, user_id, msg_ids=None):
        """
        基于 msg_id 删除消息
        """
        qs = {"user_id": user_id}
        if msg_ids:
            qs["_id"] = {"$in": [ObjectId(x) for x in msg_ids]}
        await self._col_messages.delete_many(qs)

    async def truncate_messages(self, user_id, session_id, max_count: int):
        """
        保持消息在一定的数量内，删除最早的消息
        警告：这个函数非常慢，别卡在主流程中
        """
        qs = {"user_id": user_id, "session_id": session_id}
        deleted_ids = [
            doc["_id"] async for doc in self._col_messages.find(qs, {"_id": -1}).sort("created_at", -1).skip(max_count)
        ]
        if deleted_ids:
            await self._col_messages.delete_many({"_id": {"$in": deleted_ids}})

    async def set_message_ext_attr(self, msg_id, ext_attr: dict[MessageAttr, str]):
        await self._col_messages.update_one(
            {"_id": ObjectId(msg_id)},
            [
                {
                    "$set": {
                        "attrs": {
                            "$mergeObjects": [
                                "$attrs",
                                {k.value: v for k, v in ext_attr.items()},
                            ],
                        },
                    }
                }
            ],
        )

    async def del_message_ext_attr(self, msg_id, ext_attr: List[MessageAttr]):
        await self._col_messages.update_one(
            {"_id": ObjectId(msg_id)},
            {"$unset": {f"attrs.{k.value}": "" for k in ext_attr}},
        )
