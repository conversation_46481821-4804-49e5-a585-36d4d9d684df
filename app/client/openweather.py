# coding = utf-8
import httpx
from datetime import datetime

from config.settings import settings
from utils.http_client import JSONHttpClient

from utils import logging

LOG = logging.getLogger(__name__)


class OpenWeatherClient(JSONHttpClient):
    # 2.5 的版本 API 没找到。。
    BASE_URL = "https://api.openweathermap.org/data/2.5/"

    class OpenWeatherAuth(httpx.Auth):
        def __init__(self, api_key):
            super().__init__()
            self._api_key = api_key

        def auth_flow(self, request: httpx.Request):
            request.url = request.url.copy_add_param("appid", self._api_key)
            yield request

    def __init__(self, api_key: str):
        super().__init__(
            ssl_verify=True,  # OpenWeather 开启证书验证应该没毛病
            timeout=5.0,  # 不确定这个经验值怎么来的
            auth=OpenWeatherClient.OpenWeatherAuth(api_key),
            base_url=OpenWeatherClient.BASE_URL,
        )

    async def _search(self, route, query):
        try:
            return await self.method("GET", route, params=query, follow_redirects=True)
        except Exception as e:
            LOG.error(f"OpenWeather API error: {str(e)}")
            return None

    async def get_current_weather(self, location, units: str = "metric", lang: str = "en") -> dict:
        """
        获取当前天气数据
        """
        query = {"q": location, "units": units, "lang": lang}
        return await self._search("weather", query)

    async def get_forecast_weather(
        self,
        location: str,
        target_date: datetime,
        units: str = "metric",
        lang: str = "en",
    ) -> dict:
        """
        获取天气预报数据

        @param location: 城市名称
        @param target_date: 目标日期
        """
        query = {"q": location, "units": units, "lang": lang}
        data = await self._search("forecast", query)
        if data is None:
            return None
        # 查找匹配日期的预报
        for forecast in data["list"]:
            forecast_time = datetime.fromtimestamp(forecast["dt"])
            print(forecast_time, target_date)
            if forecast_time.date() == target_date.date():
                return {"city": data["city"], "forecast": forecast}
        return None


openweather_client = OpenWeatherClient(
    api_key=settings.OPENWEATHER_API_KEY,
)
