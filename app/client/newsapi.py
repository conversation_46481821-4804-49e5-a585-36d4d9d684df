# coding = utf-8
import asyncio
import contextlib

from eventregistry import QueryArticlesIter, QueryItems, EventRegistry

from config.settings import settings
from models.web_search import ResultItem


class NewsAPIClient:
    def __init__(self, api_key: str) -> None:
        self._client = EventRegistry(apiKey=api_key, allowUseOfArchive=False)
        super().__init__()

    def sync_query(self, query_str: str, limit: int = settings.SEARXNG_DOC_LIMIT):
        result = [] # noqa 这个注释不要去掉，不然抛异常时会炸
        with contextlib.suppress(Exception):
            q = QueryArticlesIter(
                keywords=query_str,
                sourceLocationUri=QueryItems.OR(
                    ['http://en.wikipedia.org/wiki/China',
                     'http://en.wikipedia.org/wiki/Czech_Republic',
                     'http://en.wikipedia.org/wiki/Denmark',
                     'http://en.wikipedia.org/wiki/Netherlands',
                     'http://en.wikipedia.org/wiki/United_Kingdom',
                     'http://en.wikipedia.org/wiki/Finland',
                     'http://en.wikipedia.org/wiki/United_States',
                     'http://en.wikipedia.org/wiki/Germany',
                     'http://en.wikipedia.org/wiki/France',
                     'http://en.wikipedia.org/wiki/Italy',
                     'http://en.wikipedia.org/wiki/Spain',
                     'http://en.wikipedia.org/wiki/Japan',
                     'http://en.wikipedia.org/wiki/South_Korea',
                     'http://en.wikipedia.org/wiki/Norway',
                     'http://en.wikipedia.org/wiki/Poland',
                     'http://en.wikipedia.org/wiki/Portugal',
                     'http://en.wikipedia.org/wiki/Russia',
                     'http://en.wikipedia.org/wiki/Sweden',
                     'http://en.wikipedia.org/wiki/Turkey',
                     'http://en.wikipedia.org/wiki/Ukraine']
                    ),
                ignoreSourceGroupUri="paywall/paywalled_sources",
                dataType=["news", "pr"])

            for article in q.execQuery(self._client, sortBy="date", sortByAsc=False, maxItems=limit):
                result.append(ResultItem(title=article["title"], content=article["body"]))
        return result

    async def query(self, query_str: str, limit: int = settings.SEARXNG_DOC_LIMIT):
        return await asyncio.to_thread(self.sync_query, query_str, limit)


newsapi_client = NewsAPIClient(settings.NEWSAPI_API_KEY)
