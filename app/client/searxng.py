# coding = utf-8
import enum
from typing import Optional

from pydantic import HttpUrl

from config.settings import settings
from models.web_search import ResultItem
from utils.http_client import JSONHttpClient
from utils import logging

LOG = logging.getLogger(__name__)


class SearXNGClient(JSONHttpClient):
    def __init__(self, addr: HttpUrl) -> None:
        super().__init__(
            ssl_verify=False,  # 内部服务，不验证 SSL
            base_url=str(addr),
        )

    async def _search(
        self,
        query_str,
        fmt: str = "json",
        limit: int = settings.SEARXNG_DOC_LIMIT,
    ):
        try:
            return await self.method(
                "POST",
                "search",
                params={
                    "q": query_str,
                    "format": fmt,
                    "count": limit,
                },
            )
        except Exception as e:
            LOG.error(f"SearXNG API error: {str(e)}")
            return None

    async def query(
        self,
        query_str,
        category: Optional[str] = None,
        lang=None,
        limit: int = settings.SEARXNG_DOC_LIMIT,
    ):
        if category:
            query_str = f"!{category} {query_str}"
        if lang:
            query_str = f":{lang} {query_str}"

        data = await self._search(query_str, limit=limit)
        if data is None:
            return None

        result = []
        for item in data["results"]:
            try:
                result.append(ResultItem(title=item["title"], content=item["content"]))
            except Exception as e:
                LOG.error(f"parse item failed due to {e}, item = {item}")
        return result


searxng_client = SearXNGClient(settings.SEARXNG_URL)
