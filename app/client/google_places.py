# coding = utf-8
import httpx

from utils.http_client import JSONHttpClient
from config.settings import settings
from utils import logging

LOG = logging.getLogger(__name__)


class GooglePlacesClient(JSONHttpClient):
    DEFAULT_FIELDS = [
        "places.id",
        "places.displayName",
        "places.formattedAddress",
        "places.shortFormattedAddress",
        "places.areaSummary",
        "places.primaryTypeDisplayName",
    ]

    class GooglePlacesAPIAuthenticate(httpx.Auth):
        def __init__(self, api_key):
            super().__init__()
            self._api_key = api_key

        def auth_flow(self, request):
            # Send the request, with a custom `X-Authentication` header.
            request.headers["X-Goog-Api-Key"] = self._api_key
            yield request

    def __init__(self, api_key) -> None:
        super().__init__(
            ssl_verify=True,
            auth=GooglePlacesClient.GooglePlacesAPIAuthenticate(api_key),
        )

    async def transform_response(self, resp):
        data = await super().transform_response(resp)
        return (data or {}).get("places", [])

    async def _search(self, route, body, fields=DEFAULT_FIELDS, headers=None) -> httpx.Response:
        headers = headers or {}
        headers.update(
            {
                "X-Goog-FieldMask": ",".join([x.strip() for x in fields]),
            }
        )
        try:
            return await self.method(
                "POST",
                f"https://places.googleapis.com/v1/places:{route}",
                json=body,
                headers=headers,
                follow_redirects=True,
            )
        except Exception as e:
            LOG.error(f"Google Places API error: {str(e)}")
            return None

    async def search_nearby(
        self,
        included_types,
        center_latitude,
        center_longitude,
        radius_meter,
        langCode="en",
        limit=settings.SEARCH_NEARBY_MAX_RESULT_COUNT,
    ):
        return await self._search(
            route="searchNearby",
            body={
                "includedTypes": included_types,
                "maxResultCount": limit,
                "languageCode": langCode,
                "rankPreference": "DISTANCE",  # 基于距离结果排序
                "locationRestriction": {
                    "circle": {
                        "center": {
                            "latitude": center_latitude,
                            "longitude": center_longitude,
                        },
                        "radius": radius_meter,
                    }
                },
            },
        )

    async def search_text(self, query):
        return await self._search(
            route="searchText",
            body={
                "textQuery": query,
                "rankPreference": "DISTANCE",
            },
        )


google_places_client = GooglePlacesClient(settings.GOOGLE_PLACES_API_KEY)
