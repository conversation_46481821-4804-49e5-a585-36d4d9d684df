from typing import Optional, <PERSON><PERSON>
from fastapi import Request
from starlette.requests import Request
from starlette.routing import Match
from core.metrics.base import BaseMetricsLabel, Counter, Gauge, Histogram
from models.common import Context


__all__ = [
    "IntentMetric",
    "IntentCategoryMetric",
    "INTENT_PROCESSING_TIME",
    "INTENT_IN_PROGRESS",
    "INTENT_COUNTER",
    "INTENT_DISTRIBUTION",
]


class IntentMetric(BaseMetricsLabel):

    def __init__(self, ctx: Context, *args, **kwargs):
        super().__init__(
            components="Intent",
            *args,
            **kwargs,
        )


class IntentCategoryMetric(IntentMetric):
    intent: str

    def __init__(self, ctx: Context, intent: str):
        super().__init__(ctx, intent=intent)


INTENT_PROCESSING_TIME = Histogram(
    "intent_processing_time",
    "duration of intent processing (s)",
    IntentMetric,
)


INTENT_IN_PROGRESS = Gauge(
    "intent_in_progres",
    "number of intents in progress",
    IntentMetric,
)

INTENT_COUNTER = Counter(
    "intent_counter",
    "number of intents",
    IntentCategoryMetric,
)


INTENT_DISTRIBUTION = Histogram(
    "intent_distribution",
    "distribution of intents",
    IntentCategoryMetric,
)
