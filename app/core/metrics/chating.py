from core.metrics.base import Counter, Gauge, Histogram, BaseMetricsLabel
from models.common import Context

__all__ = [
    "ChattingMetric",
    "ChattingToolMetric",
    "CHATTING_REQUESTS",
    "CHATTING_REQUESTS_IN_PROGRESS",
    "CHATTING_PROCESSING_TIME",
    "CHATTING_MESSAGE_PROCESSING_TIME",
    "CHATTING_FIRST_RESPONSE_TIME",
    "CHATTING_TOOL_REQUESTS",
    "CHATTING_TOOL_REQUESTS_IN_PROGRESS",
    "CHATTING_TOOL_PROCESSING_TIME",
]


class ChattingMetric(BaseMetricsLabel):
    model_backend: str
    model_name: str

    def __init__(self, ctx: Context, backend, model, *args, **kwargs):
        super().__init__(
            components="Chatting",
            model_backend=backend,
            model_name=model,
            *args,
            **kwargs,
        )


class ChattingToolMetric(BaseMetricsLabel):
    tool: str

    def __init__(self, ctx: Context, tool_name: str, *args, **kwargs):
        super().__init__(
            components="ChattingTool",
            tool=tool_name,
            *args,
            **kwargs,
        )


CHATTING_REQUESTS = Counter(
    "chatting_requests_total",
    "number of chatting requests",
    ChattingMetric,
)

CHATTING_REQUESTS_IN_PROGRESS = Gauge(
    "chatting_requests_in_progress",
    "number of chatting requests in progress",
    ChattingMetric,
)


CHATTING_PROCESSING_TIME = Histogram(
    "chatting_requests_duration_seconds",
    "total duration of chatting requests (s)",
    ChattingMetric,
)


CHATTING_FIRST_RESPONSE_TIME = Histogram(
    "chatting_requests_first_token_duration_seconds",
    "duration of the first token response in chatting requests (s)",
    ChattingMetric,
)


CHATTING_TOOL_REQUESTS = Counter(
    "chatting_tool_requests_total",
    "number of tool requests",
    ChattingToolMetric,
)

CHATTING_TOOL_REQUESTS_IN_PROGRESS = Gauge(
    "chatting_tool_requests_in_progress",
    "number of tool requests in progress",
    ChattingToolMetric,
)

CHATTING_TOOL_PROCESSING_TIME = Histogram(
    "chatting_tool_requests_duration_seconds",
    "duration of tool requests (s)",
    ChattingToolMetric,
)


CHATTING_MESSAGE_PROCESSING_TIME = Histogram(
    "chatting_requests_message_processing_duration_seconds",
    "duration of message processing in chatting requests (s)",
    ChattingMetric,
)
