import time
from threading import Lock
from typing import Op<PERSON>, Dict, cast
from pydantic import BaseModel

from config.settings import RunMode, settings
from contextlib import contextmanager

from prometheus_client import Counter as Prometheus<PERSON><PERSON>nter, \
    Gauge as Prometheus<PERSON>auge, Histogram as PrometheusHistogram
from ray.util.metrics import <PERSON> as <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ge as <PERSON><PERSON><PERSON><PERSON>, \
    Histogram as RayHistogram


__all__ = [
    "BaseMetricsLabel",
    "time_consumed",
    "in_progress",
    "Counter",
    "Gauge",
    "Histogram",
]


class BaseMetricsLabel(BaseModel):
    app_name: str
    components: str

    def __init__(self, components, *args, **kwargs):
        super().__init__(app_name=settings.APP_NAME, components=components, *args, **kwargs)

    def to_labels(self):
        return self.model_dump(mode="json")

class BaseMetric:
    """Base class for all metrics to reduce code duplication"""
    model: type[BaseMetricsLabel]

    def __init__(self, name: str, desc: str, model: type[BaseMetricsLabel]):
        self.model = model
        self.name = name
        self.desc = desc
        self._setup_metric()

    def _setup_metric(self):
        """Override in subclasses to setup specific metric types"""
        raise NotImplementedError

    def _validate_model(self, model: BaseMetricsLabel):
        """Validate that the model type matches expectations"""
        assert type(model) is self.model, f"Invalid model type: {type(model).__name__} != {self.model.__name__}"

    def _get_labels_dict(self, model: BaseMetricsLabel) -> dict:
        """Get labels dictionary from model"""
        return model.to_labels()

    def _get_labels_keys(self) -> tuple:
        """Get label keys for metric initialization"""
        return tuple(self.model.model_fields.keys())


class Counter(BaseMetric):
    def _setup_metric(self):
        if settings.RUN_MODE == RunMode.FASTAPI:
            self.metric: PrometheusCounter = PrometheusCounter(self.name, self.desc, self._get_labels_keys()) # type: ignore
            self.inc = self._prometheus_inc
        else:
            self.metric: RayCounter = RayCounter(self.name, self.desc, self._get_labels_keys())
            self.inc = self._ray_inc

    def inc(self, model: BaseMetricsLabel, value=1):
        pass

    def _prometheus_inc(self, model: BaseMetricsLabel, value=1):
        self._validate_model(model)
        metric = cast(PrometheusCounter, self.metric)
        metric.labels(**self._get_labels_dict(model)).inc(value)

    def _ray_inc(self, model: BaseMetricsLabel, value=1):
        self._validate_model(model)
        metric = cast(RayCounter, self.metric)
        metric.inc(value, tags=self._get_labels_dict(model))

class Gauge(BaseMetric):
    value: float
    lock: Lock

    def __init__(self, name: str, desc: str, model: type[BaseMetricsLabel]):
        self.lock = Lock()
        self.value = 0
        super().__init__(name, desc, model)

    def _setup_metric(self):
        if settings.RUN_MODE == RunMode.FASTAPI:
            self.metric: PrometheusGauge = PrometheusGauge(self.name, self.desc, self._get_labels_keys()) # type: ignore
            self.set = self._prometheus_set
            self.inc = self._prometheus_inc
            self.dec = self._prometheus_dec
        else:
            self.metric: RayGauge = RayGauge(self.name, self.desc, self._get_labels_keys())
            self.set = self._ray_set
            self.inc = self._ray_inc
            self.dec = self._ray_dec

    def set(self, model: BaseMetricsLabel, value: float):
        pass

    def inc(self, model: BaseMetricsLabel, value: float=1):
        pass

    def dec(self, model: BaseMetricsLabel, value: float=1):
        pass

    def _prometheus_set(self, model: BaseMetricsLabel, value: float):
        self._validate_model(model)
        metric = cast(PrometheusGauge, self.metric)
        metric.labels(**self._get_labels_dict(model)).set(value)

    def _ray_set(self, model: BaseMetricsLabel, value: float):
        self._validate_model(model)
        with self.lock:
            self.value = value
        metric = cast(RayGauge, self.metric)
        metric.set(self.value, tags=self._get_labels_dict(model))

    def _prometheus_inc(self, model: BaseMetricsLabel, value: float=1):
        self._validate_model(model)
        metric = cast(PrometheusGauge, self.metric)
        metric.labels(**self._get_labels_dict(model)).inc(value)

    def _ray_inc(self, model: BaseMetricsLabel, value: float=1):
        self._validate_model(model)
        with self.lock:
            self.value = value
        metric = cast(RayGauge, self.metric)
        metric.set(self.value, tags=self._get_labels_dict(model))

    def _prometheus_dec(self, model: BaseMetricsLabel, value: float=1):
        self._validate_model(model)
        metric = cast(PrometheusGauge, self.metric)
        metric.labels(**self._get_labels_dict(model)).dec(value)

    def _ray_dec(self, model: BaseMetricsLabel, value: float=1):
        self._validate_model(model)
        with self.lock:
            self.value -= value
        metric = cast(RayGauge, self.metric)
        metric.set(self.value, tags=self._get_labels_dict(model))


DEFAULT_BOUNDARIES = [0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, \
                        0.75, 1.0, 2.5, 5.0, 7.5, 10.0, float('inf')]
class Histogram(BaseMetric):
    def __init__(self, name: str, desc: str, model: type[BaseMetricsLabel],
                  boundaries: list[float]=DEFAULT_BOUNDARIES):
        self.boundaries = boundaries
        super().__init__(name, desc, model)

    def _setup_metric(self):
        if settings.RUN_MODE == RunMode.FASTAPI:
            self.metric: PrometheusHistogram = PrometheusHistogram(self.name, self.desc, self._get_labels_keys(), buckets=self.boundaries) # type: ignore
            self.observe = self._prometheus_observe
        else:
            self.metric: RayHistogram = RayHistogram(self.name, self.desc, self.boundaries)
            self.observe = self._ray_observe

    def observe(self, model: BaseMetricsLabel, value: float, \
                exemplar: Optional[Dict[str, str]] = None):
        pass

    def _prometheus_observe(self, model: BaseMetricsLabel, value: float, \
                            exemplar: Optional[Dict[str, str]] = None):
        self._validate_model(model)
        metric = cast(PrometheusHistogram, self.metric)
        metric.labels(**self._get_labels_dict(model)).observe(value, exemplar=exemplar)

    def _ray_observe(self, model: BaseMetricsLabel, value: float, \
                     exemplar: Optional[Dict[str, str]] = None):
        self._validate_model(model)
        metric = cast(RayHistogram, self.metric)
        metric.observe(value, tags=self._get_labels_dict(model))


@contextmanager
def time_consumed(metric: BaseMetricsLabel, counter: Histogram):
    """
    返回一个 context, 在结束的时候写入指标
    """
    start_at = time.perf_counter()
    try:
        yield
    finally:
        counter.observe(metric, time.perf_counter() - start_at)


@contextmanager
def in_progress(metric: BaseMetricsLabel, gauge: Gauge):
    try:
        gauge.inc(metric)
        yield
    finally:
        gauge.dec(metric)
