from typing import <PERSON><PERSON>
from fastapi import Request
from starlette.requests import Request
from starlette.routing import Match
from core.metrics.base import BaseMetricsLabel, Counter, Gauge, Histogram

__all__ = [
    "FastApiMetric",
    "FastApiExceptionMetric",
    "FastApiStatusCodeMetric",
    "REQUESTS",
    "REQUESTS_IN_PROGRESS",
    "REQUESTS_PROCESSING_TIME",
    "EXCEPTIONS",
    "RESPONSES",
]


class FastApiMetric(BaseMetricsLabel):
    method: str
    path: str

    def __init__(self, req: Request, *args, **kwargs):
        route, _ = self.get_path(req)
        super().__init__(method=req.method, path=route, components="FastAPI", *args, **kwargs)

    @staticmethod
    def get_path(request: Request) -> Tuple[str, bool]:
        for route in request.app.routes:
            match, child_scope = route.matches(request.scope)
            if match == Match.FULL:
                return route.path, True

        return request.url.path, False


class FastApiExceptionMetric(FastApiMetric):

    def __init__(self, req: Request, exc: Exception):
        super().__init__(req, exception_type=type(exc).__name__)


class FastApiStatusCodeMetric(FastApiMetric):

    def __init__(self, req: Request, status_code: int):
        super().__init__(req, status_code=status_code)


REQUESTS = Counter(
    "fastapi_requests_total",
    "number of requests",
    FastApiMetric,
)

RESPONSES = Counter(
    "fastapi_responses_total",
    "number of responses",
    FastApiStatusCodeMetric,
)

REQUESTS_PROCESSING_TIME = Histogram(
    "fastapi_requests_duration_seconds",
    "duration of requests (s)",
    FastApiMetric,
)

EXCEPTIONS = Counter(
    "fastapi_exceptions_total",
    "number of exceptions",
    FastApiExceptionMetric,
)

REQUESTS_IN_PROGRESS = Gauge(
    "fastapi_requests_in_progress",
    "number of requests in progress",
    FastApiMetric,
)
