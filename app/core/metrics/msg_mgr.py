from core.metrics import BaseMetricsLabel, Gauge

__all__ = [
    "MsgMgrMetric",
    "MSG_MGR_QSIZE_GAUGE",
    "MSG_MGR_WRITE_SIZE_GAUGE",
    "MSG_MGR_DELAY_GAUGE"
]


class MsgMgrMetric(BaseMetricsLabel):

    def __init__(self, *args, **kwargs):
        super().__init__(
            components="MsgMgr",
            *args,
            **kwargs,
        )


MSG_MGR_QSIZE_GAUGE = Gauge(
    "msg_mgr_qsize",
    "number of pending messages waiting to be written to mongodb",
    MsgMgrMetric,
)


MSG_MGR_WRITE_SIZE_GAUGE = Gauge(
    "msg_mgr_write_size",
    "number of messages being written to mongodb",
    MsgMgrMetric,
)

MSG_MGR_DELAY_GAUGE = Gauge(
    "msg_mgr_delay",
    "delay of writing messages to mongodb (s)",
    MsgMgrMetric,
)

