from config.settings import settings
from utils import logging
from opentelemetry import trace
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.resources import Resource
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter


class DeploymentMixin:
    def __init__(self, *args, **kwargs):
        logging.init_logger()
        super().__init__(*args, **kwargs)

def ray_setup_tracing() -> None:
    # Setting OpenTelemetry
    # set the service name to show in traces
    resource = Resource.create(attributes={"service.name": settings.APP_NAME, "compose_service": settings.APP_NAME})

    # set the tracer provider
    tracer = TracerProvider(resource=resource)
    trace.set_tracer_provider(tracer)

    tracer.add_span_processor(BatchSpanProcessor(OTLPSpanExporter(endpoint=settings.TEMPO_GRPC_ENDPOINT)))
