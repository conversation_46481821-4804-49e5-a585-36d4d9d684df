from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from middlewares.metrics import PrometheusMiddleware, setup_otlp
from routers import manager_router, chat_router, intent_router, metrics_router
from config.settings import settings


class FastApiFactory:

    def __init__(self):
        self.app = FastAPI()
        self._processors = []
        self._routers = [
            manager_router.router,
            chat_router.router,
            intent_router.router,
            metrics_router.router,
        ]
        for router in self._routers:
            self.app.include_router(router)

        @self.app.get("/")
        async def health():
            return "OK"

    def enable_cors(self, cors_allow_domains):
        def chain():
            if cors_allow_domains:
                self.app.add_middleware(
                    CORSMiddleware,
                    allow_origins=cors_allow_domains,
                    allow_credentials=True,
                    allow_methods=["*"],
                    allow_headers=["*"],
                )

        self._processors.append(chain)
        return self

    def enable_prometheus(self):
        def chain():
            self.app.add_middleware(PrometheusMiddleware, app_name=settings.APP_NAME)

        self._processors.append(chain)
        return self

    def enable_otlp(self):
        def chain():
            setup_otlp(self.app, settings.APP_NAME, settings.TEMPO_GRPC_ENDPOINT)

        self._processors.append(chain)
        return self

    def get_app(self):
        return self.app

    def setup(self):
        for processor in self._processors:
            processor()
        return self
