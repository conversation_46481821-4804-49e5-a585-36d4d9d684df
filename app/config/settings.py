# coding = utf-8
import enum
import os.path

from pydantic_settings import BaseSettings
from pydantic import HttpUrl


class TitleGenRule(str, enum.Enum):
    FIRST_QUESTION = "first_question"
    SUMMARY = "summary"  # LLM


class RunMode(str, enum.Enum):
    RAY = "ray"  # 云上模式
    RAY_LOCAL = "ray-local"  # 本地单机 Ray
    FASTAPI = "fastapi"  # FastAPI


class EnvCategory(str, enum.Enum):
    DEV = "dev"
    TEST = "test"
    RELEASE = "release"


class Settings(BaseSettings):
    RUN_MODE: RunMode = RunMode.RAY
    ENV: EnvCategory = EnvCategory.DEV

    # Ray配置
    MAX_ONGOING_REQUESTS: int = 100

    # MongoDB 配置
    MONGO_USERNAME: str = "appaijarvis"
    MONGO_PASSWORD: str = "kQ8hD2kT3aC4mC4"
    MONGO_HOST: str = "mongodb-service"
    MONGO_DB_NAME: str = "ai-jarvis"
    MONGO_AUTH_SOURCE: str = "ai-jarvis"

    # REDIS 配置
    REDIS_HOST: str = "127.0.0.1"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: str = ""

    # Cache
    CACHE_MESSAGE_LIST_DB: int = 0

    """
    会话配置, 两个配置都需要满足

    HISTORY_MSG 表示只拉去近期 N 条消息
    HISTORY_MSG_TTL_SECONDS 表示只接受 M 秒内的消息

    合并条件为： 只提取 M 秒内，最多 N 条消息

    PS: 由于会话是以「轮」计算，MAX_HISTORY_MESSAGES 必须是2的倍数，不然会出现奇怪的问题
    """
    MAX_HISTORY_MESSAGES: int = 20
    MAX_HISTORY_MESSAGE_TTL_SECONDS: int = 10 * 60  # 10 分钟
    HISTORY_TTL_SECONDS: int = 24 * 60 * 60  # 24 小时, 免得内存爆炸

    PERSISTENT_MESSAGE_QUEUE_SIZE: int = 512

    # 生成带认证的URI
    @property
    def MONGO_URI(self):
        if self.MONGO_USERNAME and self.MONGO_PASSWORD:
            return f"mongodb://{self.MONGO_USERNAME}:{self.MONGO_PASSWORD}@{self.MONGO_HOST}/{self.MONGO_DB_NAME}?authSource={self.MONGO_AUTH_SOURCE}&retryWrites=true&w=majority"
        return f"mongodb://{self.MONGO_HOST}?retryWrites=true&w=majority"

    # searxng addr
    SEARXNG_URL: HttpUrl = "https://searxng.ev3n.co/"
    SEARXNG_DOC_LIMIT: int = 10

    # news api
    NEWSAPI_API_KEY: str = "85234410-9b02-4360-88c2-dfa015ecc306"
    NEWSAPI_DOC_LIMIT: int = 10

    # search cache
    SEARCH_CACHE_TTL: int = 24 * 2600
    SEARCH_CACHE_MAX_SIZE: int = int(1e5)

    # location cache
    LOCATION_CACHE_TTL: int = 24 * 2600 * 30
    LOCATION_CACHE_MAX_SIZE: int = int(1e5)

    # news cache
    NEWS_CACHE_TTL: int = 24 * 2600 * 30
    NEWS_CACHE_MAX_SIZE: int = int(1e5)

    # OpenWeatherMap
    # TODO: 从secrets中读取
    OPENWEATHER_API_KEY: str = "********************************"

    # Mistral
    MISTRAL_API_KEY: str = "6VJEOk3nslHVzg0this6jzTIYXzzaEqe"
    MISTRAL_BASE_URL: str = "https://api.mistral.ai/v1"

    # faster whisper
    FASTER_WHISPER_THREADS: int = 4
    FASTER_WHISPER_WORKERS: int = 1
    FASTER_WHISPER_BATCH_SIZE: int = 8

    # google places api
    GOOGLE_PLACES_API_KEY: str = "AIzaSyCneAKWT4b61rG0ykbGRbLNK7gfKsy0GEU"
    SEARCH_NEARBY_RADIUS_METER: int = 5000
    SEARCH_NEARBY_MAX_RESULT_COUNT: int = 5

    # google genai
    GOOGLE_AI_API_KEY: str = "AIzaSyDtGlzAYTYa97jcUGIu5NUMKohJi9s4aWQ"

    # semantic
    ROOT_DIR: str = os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    RESOURCES_DIR: str = os.path.join(ROOT_DIR, "resources")
    SEMANTIC_BATCH_SIZE: int = 10
    SEMANTIC_THRESHOLD: float = 0.7

    AIAGENT_BACKEND: str = "business.ai.backend.GEMINI_2_0_FLASH"

    # Title 生成规则
    SESSION_TITLE_GEN_RULE: str = TitleGenRule.FIRST_QUESTION

    # Log 配置
    LOG_LEVEL: str = "DEBUG"
    LOG_PATH: str = "./logs"

    # 杂项
    APP_NAME: str = "Jarvis"
    TEMPO_GRPC_ENDPOINT: str = "http://tempo-service:4317"  # tempo 的 GRPC endpoint，用于采集 trace


settings = Settings()
