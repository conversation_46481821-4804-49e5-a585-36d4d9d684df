from dependencies.ray_client import ray_ai_service_client, ray_intent_service_client, ray_transcriber_service_client
from config.settings import RunMode, settings

__all__ = [
    "get_ai_service_client",
    # "get_transcriber_client",
    "get_intent_service_client",
]


_CACHE = {}


def cache_it(func):
    def wrapper():
        key = func.__name__
        if key not in _CACHE:
            _CACHE[key] = func()
        return _CACHE[key]

    return wrapper


@cache_it
def get_ai_service_client():
    if settings.RUN_MODE in {RunMode.RAY, RunMode.RAY_LOCAL}:
        return ray_ai_service_client()
    else:
        from business.ai.ai_agent import AIAgentService

        return AIAgentService()


@cache_it
def get_transcriber_client():
    if settings.RUN_MODE in {RunMode.RAY, RunMode.RAY_LOCAL}:
        return ray_transcriber_service_client()
    else:
        from business.transcribe.transcribe import TranscribeService

        return TranscribeService()


@cache_it
def get_intent_service_client():
    if settings.RUN_MODE in {RunMode.RAY, RunMode.RAY_LOCAL}:
        return ray_intent_service_client()
    else:
        from business.semantic.intent import IntentService

        return IntentService()
