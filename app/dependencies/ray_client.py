from ray import serve


class RayProxyFunctionCall:
    def __init__(self, obj, options=None):
        self.__obj = obj
        self.__options = options

    def __call__(self, *args, **kwargs):
        ptr = self.__obj
        if self.__options:
            ptr = ptr.options(**self.__options)
        return ptr.remote(*args, **kwargs)

    def options(self, options=None):
        return RayProxyFunctionCall(self.__obj, options)

    def __getattr__(self, item):
        return RayProxyFunctionCall(getattr(self.__obj, item), self.__options)


def ray_ai_service_client():
    return RayProxyFunctionCall(serve.get_deployment_handle("AIAgentDeployment"), options={"stream": True})


def ray_transcriber_service_client():
    return RayProxyFunctionCall(serve.get_deployment_handle("TranscriberServe"))


def ray_intent_service_client():
    return RayProxyFunctionCall(serve.get_deployment_handle("SemanticIntentDeployment"))
