# coding = utf-8
from ray import serve
from business.transcribe import TranscribeService
from config.settings import settings
from core.ray_helper import DeploymentMixin


@serve.deployment(
    num_replicas=1,
    health_check_period_s=120,
    ray_actor_options={"num_cpus": settings.FASTER_WHISPER_WORKERS * settings.FASTER_WHISPER_THREADS},
)
class TranscriberServe(DeploymentMixin):
    def __init__(self):
        super().__init__()
        self.transcribe_service = TranscribeService()

    async def transcribe(self, audio_bytes: bytes):
        transcription = await self.transcribe_service.transcribe_audio(audio_bytes)

        return {
            "transcription": transcription,
        }

    async def streaming_transcribe(self, client_id: str, audio_chunk: bytes):
        text = await self.transcribe_service.transcirbe_audio_stream(client_id, audio_chunk)
        return text
