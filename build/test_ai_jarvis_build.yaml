steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: [ 'build', '-t', 'us-central1-docker.pkg.dev/${PROJECT_ID}/ai/ai-jarvis:${SHORT_SHA}', '--build-arg', 'PROJECT_ID=${PROJECT_ID}', '--build-arg', 'BASE_TAG=${_BASE_TAG}', '.' ]
  - name: 'gcr.io/cloud-builders/docker'
    args: [ 'push',  'us-central1-docker.pkg.dev/${PROJECT_ID}/ai/ai-jarvis:${SHORT_SHA}' ]
  - name: 'gcr.io/$PROJECT_ID/helm'
    args: [ 'upgrade', '--install', 'ai-jarvis', '-f', 'build/ai-jarvis/test_values.yaml', 'build/ai-jarvis', '--set', 'image=us-central1-docker.pkg.dev/even-tp-46351/ai/ai-jarvis:${SHORT_SHA}', '--debug' ]
    env:
      - 'CLOUDSDK_COMPUTE_REGION=us-central1'
      - 'CLOUDSDK_CONTAINER_CLUSTER=ai-jarvis'
options:
  logging: CLOUD_LOGGING_ONLY
images:
  - us-central1-docker.pkg.dev/${PROJECT_ID}/ai/ai-jarvis