apiVersion: v1
kind: ConfigMap
metadata:
  name: jarvis-config
data:
  alloy-config: |-
{{ tpl (.Files.Get "config/alloy.alloy" ) .  | indent 4 }}

---
apiVersion: ray.io/v1
kind: RayService
metadata:
  name: ai
spec:
  serviceUnhealthySecondThreshold: 300
  deploymentUnhealthySecondThreshold: 300
  serveConfigV2: |
{{ toYaml .Values.serve_config | indent 4 }}
  rayClusterConfig:
    rayVersion: '2.41.0' # Should match Ray version in the containers
    headGroupSpec:
      rayStartParams:
        dashboard-host: '0.0.0.0'
        num-cpus: "{{ sub .Values.head_resource.limits.cpu 1 }}"
        # tracing-startup-hook: core.ray_helper:ray_setup_tracing
      template:
        metadata:
          annotations:
            gke-gcsfuse/volumes: "true"
        spec:
          containers:
            - name: ray-head
              image: {{ .Values.image }}
              env:
{{ toYaml .Values.ray_env | indent 16 }}
{{ toYaml .Values.head_env | indent 16 }}
              resources:
                limits:
{{ toYaml .Values.head_resource | indent 16 }}
              securityContext:
                capabilities:
                  add:
                    - SYS_PTRACE
              ports:
                - containerPort: 6379
                  name: gcs-server
                - containerPort: 8265 # Ray dashboard
                  name: dashboard
                - containerPort: 10001
                  name: client
                - containerPort: 8000
                  name: serve
              volumeMounts:
                - name: model
                  mountPath: /ai-jarvis/resources
                  readOnly: true
                - name: logs
                  mountPath: /ai-jarvis/logs
            - name: ray-alloy
              image: grafana/alloy:v1.8.0
              command: 
                - alloy
                - run
                - /etc/alloy/jarvis-log.alloy
              volumeMounts:
                - name: logs
                  mountPath: /ai-jarvis/logs
                - name: config
                  mountPath: /etc/alloy
          {{- if $.Values.serviceAccounts }}
          serviceAccountName: {{ $.Values.serviceAccounts }}
          {{- end}}
          volumes:
            - name: model
              csi:
                driver: gcsfuse.csi.storage.gke.io
                volumeAttributes:
                  bucketName: {{ .Values.middlewares.model.bucket }}
                  mountOptions: "implicit-dirs"
            - name: logs
              emptyDir: {}
            - name: config
              configMap:
                name: jarvis-config
                items:
                - key: alloy-config
                  path: jarvis-log.alloy
    workerGroupSpecs:
      - groupName: small-group
{{ toYaml .Values.worker_replicas | indent 8 }}
        rayStartParams:
          num-cpus: "{{ sub .Values.worker_resource.limits.cpu 1 }}"
        template:
          metadata:
            annotations:
              gke-gcsfuse/volumes: "true"
          spec:
            containers:
              - name: ray-worker
                image: {{ .Values.image }}
                securityContext:
                  capabilities:
                    add:
                      - SYS_PTRACE
                env:
{{ toYaml .Values.ray_env | indent 20 }}
                lifecycle:
                  preStop:
                    exec:
                      command: ["/bin/sh","-c","ray stop"]
                resources:
{{ toYaml .Values.worker_resource | indent 20 }}
                volumeMounts:
                  - name: logs
                    mountPath: /ai-jarvis/logs
                  - name: model
                    mountPath: /ai-jarvis/resources
                    readOnly: true
              - name: ray-alloy
                image: grafana/alloy:v1.8.0
                command: 
                  - alloy
                  - run
                  - /etc/alloy/jarvis-log.alloy
                volumeMounts:
                  - name: logs
                    mountPath: /ai-jarvis/logs
                  - name: config
                    mountPath: /etc/alloy
            {{- if $.Values.serviceAccounts }}
            serviceAccountName: {{ $.Values.serviceAccounts }}
            {{- end}}
            volumes:
              - name: model
                csi:
                  driver: gcsfuse.csi.storage.gke.io
                  volumeAttributes:
                    bucketName: {{ .Values.middlewares.model.bucket }}
                    mountOptions: "implicit-dirs"
              - name: logs
                emptyDir: {}
              - name: config
                configMap:
                  name: jarvis-config
                  items:
                  - key: alloy-config
                    path: jarvis-log.alloy
---

apiVersion: v1
kind: Service
metadata:
  name: ai-jarvis-service
  labels:
    app: ai-jarvis
spec:
  selector:
    ray.io/node-type: head
  ports:
  - name: dashboard-port
    protocol: TCP
    port: 8265
    targetPort: 8265
  - name: serve-port
    protocol: TCP
    port: 8000
    targetPort: 8000


---


apiVersion: networking.gke.io/v1
kind: HealthCheckPolicy
metadata:
  name: ai-jarvis-healthcheck

spec:
  default:
    checkIntervalSec: 15
    config:
      type: HTTP
      httpHealthCheck:
        port: 8000
  targetRef:
    group: ""
    kind: Service
    name: ai-jarvis-service
