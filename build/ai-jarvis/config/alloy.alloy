local.file_match "default" {
    path_targets = [{
        __path__ = "/ai-jarvis/logs/*.log",
        service = "Jarvis",
    }]
}



loki.write "default" {
    endpoint {
        url = "http://{{ $.Values.middlewares.loki.host }}:{{ $.Values.middlewares.loki.port }}{{ $.Values.middlewares.loki.push_addr }}"
    }
}



loki.process "add_labels" {
  forward_to = [loki.write.default.receiver]

  stage.static_labels {
    values = {
      namespace = "{{ .Release.Namespace }}",
      app = "{{ .Release.Name }}",
    }
  }
}


loki.source.file "default" {
    targets = local.file_match.default.targets
    forward_to = [loki.process.add_labels.receiver]
}
logging {
  level  = "debug"
  format = "logfmt"
}
