

faster_whisper:
  threads: &faster_whisper_threads "1"
  workers: &faster_whisper_workers "1"
  batch_size: &faster_whisper_batch_size "8"

middlewares:
  loki:
    host: loki-service.test-ai-middleware.svc.cluster.local
    port: 3100
    push_addr: /loki/api/v1/push

  model:
    bucket: even-ai-jarvis-model


ray_env:
  - name: MONGO_USERNAME
    value: ai-jarvis
  - name: MONGO_PASSWORD
    value: kQ8hD2kT3aC4mC4
  - name: MONGO_DB_NAME
    value: appaijarvis
  - name: MONGO_HOST
    value: mongodb-service.test-ai-middleware.svc.cluster.local
  - name: MONGO_AUTH_SOURCE
    value: admin

  - name: REDIS_HOST
    value: ***********
  - name: REDIS_PASSWORD
    value: 
  - name: REDIS_PORT
    value: "6379"

  - name: FASTER_WHISPER_THREADS
    value: *faster_whisper_threads
  - name: FASTER_WHISPER_WORKERS
    value: *faster_whisper_workers
  - name: FASTER_WHISPER_BATCH_SIZE
    value: *faster_whisper_batch_size
  - name: GOOGLE_PLACES_API_KEY
    value: "AIzaSyACqV--4jpiPEr5bGXGq_FlKFrcXRxZB9g"
  - name: SEARCH_NEARBY_RADIUS_METER
    value: "5000"


head_env:
  - name: RAY_GRAFANA_IFRAME_HOST
    value: http://127.0.0.1:3000
  - name: RAY_GRAFANA_HOST
    value: http://prometheus-grafana.monitoring.svc:80
  - name: RAY_PROMETHEUS_HOST
    value: http://prometheus-kube-prometheus-prometheus.monitoring.svc:9090


worker_resource:
  limits:
    cpu: 4
    memory: 12Gi
  requests:
    cpu: 2
    memory: 8Gi


head_resource:
  limits:
    cpu: 2
    memory: 8Gi
  requests:
    cpu: 2
    memory: 4Gi


serviceAccounts: ray-test

worker_replicas:
  replicas: 1
  minReplicas: 1
  maxReplicas: 1

serve_config:
  applications:
    - name: ai-jarvis
      import_path: main:main
      route_prefix: /
      deployments:
        - name: TranscriberServe
          num_replicas: 1
          max_replicas_per_node: 1
          ray_actor_options:
            num_cpus: 1
        - name: FastAPIWrapper
          num_replicas: 1
          max_replicas_per_node: 1
          ray_actor_options:
            num_cpus: 1
        - name: AIAgentDeployment
          num_replicas: 1
          max_replicas_per_node: 1
          ray_actor_options:
            num_cpus: 1
            runtime_env:
              env_vars:
                OMP_NUM_THREADS: "1"
        - name: SemanticIntentDeployment
          num_replicas: 1
          max_replicas_per_node: 1
          ray_actor_options:
            num_cpus: 1