

faster_whisper:
  threads: &faster_whisper_threads "4"
  workers: &faster_whisper_workers "2"
  batch_size: &faster_whisper_batch_size "8"

ray_env:
  - name: MONGO_USERNAME
    value: appaijarvis
  - name: MONGO_PASSWORD
    value: kQ8hD2kT3aC4mC4
  - name: M<PERSON><PERSON><PERSON>_DB_NAME
    value: ai-jarvis
  - name: MONGO_HOST
    value: mongodb-service
  - name: MONGO_AUTH_SOURCE
    value: ai-jarvis
  - name: FASTER_WHISPER_THREADS
    value: *faster_whisper_threads
  - name: FASTER_WHISPER_WORKERS
    value: *faster_whisper_workers
  - name: FASTER_WHISPER_BATCH_SIZE
    value: *faster_whisper_batch_size
  - name: GOOGLE_PLACES_API_KEY
    value: "AIzaSyACqV--4jpiPEr5bGXGq_FlKFrcXRxZB9g"
  - name: SEARCH_NEARBY_RADIUS_METER
    value: "5000"


worker_resource:
  limits:
    cpu: 12
    memory: 12Gi
  requests:
    cpu: 8
    memory: 8Gi


head_resource:
  limits:
    cpu: 2
    memory: 8Gi
  requests:
    cpu: 2
    memory: 4Gi

worker_replicas:
  replicas: 1
  minReplicas: 1
  maxReplicas: 1

serve_config:
  applications:
    - name: ai-jarvis
      import_path: main:main
      route_prefix: /
      deployments:
        - name: TranscriberServe
          num_replicas: 1
          max_replicas_per_node: 1
          ray_actor_options:
            num_cpus: 1
        - name: FastAPIWrapper
          num_replicas: 2
          max_replicas_per_node: 1
          ray_actor_options:
            num_cpus: 1
        - name: AIAgentDeployment
          num_replicas: 2
          max_replicas_per_node: 1
          ray_actor_options:
            num_cpus: 1
        - name: SemanticIntentDeployment
          num_replicas: 2
          max_replicas_per_node: 1
          ray_actor_options:
            num_cpus: 1