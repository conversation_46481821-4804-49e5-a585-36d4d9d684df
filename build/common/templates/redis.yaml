apiVersion: v1
kind: Secret
metadata:
  name: redis-secret
type: Opaque
data:
  password: {{ .Values.redis.init_password | b64enc }}

---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-data
spec:
  accessModes:
    - ReadWriteOnce  
  resources:
    requests:
      storage: {{ $.Values.redis.storage_resources }}
  storageClassName: standard-rwo

---

apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
data:
  redis.conf: |-
{{ tpl (.Files.Get "config/redis/redis.conf" ) .  | indent 4 }}

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis 
  labels:
    app: redis
spec:
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7
        command:
          - redis-server
          - "/redis-conf/redis.conf"
        env:
        - name: MASTER
          value: "true"
        resources:
{{ toYaml $.Values.redis.resources | indent 12 }}
        ports:
        - containerPort: 6379
        volumeMounts:
        - mountPath: /redis-data
          name: redis-data
        - mountPath: /redis-conf
          name: config
      - name: redis-exporter
        image: oliver006/redis_exporter:latest
        securityContext:
          runAsUser: 59000
          runAsGroup: 59000
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
        env:
          - name: REDIS_PASSWORD
            value: {{ $.Values.redis.init_password }}
        resources:
          requests:
            cpu: 100m
            memory: 100Mi
        ports:
        - name: metrics
          containerPort: 9121

      volumes:
        - name: config
          configMap:
            name: redis-config
            items:
            - key: redis.conf
              path: redis.conf
        - name: redis-data
          persistentVolumeClaim:
            claimName: redis-data

---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
spec:
  selector:
    app: redis
  ports:
  - name: redis-port
    protocol: TCP
    port: 6379
    targetPort: 6379
