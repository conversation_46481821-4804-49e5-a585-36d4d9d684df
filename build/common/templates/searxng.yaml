apiVersion: v1
kind: ConfigMap
metadata:
  name: searxng-config
data:
  settings.yml: |-
{{ (.Files.Get "config/searxng/settings.yml") | indent 4 }}
  uwsgi.ini: |-
{{ (.Files.Get "config/searxng/uwsgi.ini") | indent 4 }}

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: searxng
  labels:
    app: searxng
spec:
  replicas: {{ .Values.searxng.replicas }}
  selector:
    matchLabels:
      app: searxng
  template:
    metadata:
      labels:
        app: searxng
    spec:
      containers:
        - name: searxng
          image: searxng/searxng
          ports:
            - containerPort: 8080
              name: http-web-svc
          env:
            - name: INSTANCE_NAME
              value: my-instance
            - name: BASE_URL
              value: http://0.0.0.0:8080
          volumeMounts:
            - name: config-volume
              mountPath: /etc/searxng
          resources:
{{ toYaml $.Values.searxng.resources | indent 12 }}
      volumes:
        - name: config-volume
          configMap:
            name: searxng-config

---
apiVersion: v1
kind: Service
metadata:
  name: searxng-service
spec:
  selector:
    app: searxng
  ports:
  - name: searxng-port
    protocol: TCP
    port: 8080
    targetPort: 8080