kind: StatefulSet
apiVersion: apps/v1
metadata:
  name: mongodb-config
  labels:
    app: mongodb-config
spec:
  serviceName: mongodb-config-service
  replicas: {{ .Values.mongodb.replicas }}
  selector:
    matchLabels:
      app: mongodb-config
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: mongodb-config
    spec:
      containers:
        - name: mongodb-config
          image: mongo:8.0.4
          command:
            - mongod
            - "--configsvr"
            - "--replSet"
            - config-rs
            - "--bind_ip_all"
          ports:
            - containerPort: 27019
              name: mongodb-config
          livenessProbe:
            tcpSocket:
              port: 27019
            initialDelaySeconds: 15
            periodSeconds: 10
          env:
            - name: MONGO_INITDB_ROOT_USERNAME
              value: {{ .Values.mongodb.mongo_initdb_root_username }}
            - name: MONG<PERSON>_INITDB_ROOT_PASSWORD
              value: {{ .Values.mongodb.mongo_initdb_root_password }}
            - name: MONGO_INITDB_DATABASE
              value: {{ .Values.mongodb.mongo_initdb_database }}
            - name: GLIBC_TUNABLES
              value: glibc.pthread.rseq=0
          volumeMounts:
            - name: mongodb-config
              mountPath: /data
              # readOnly: true
          resources:
{{ toYaml $.Values.mongodb.config_resources | indent 12 }}
  volumeClaimTemplates:
    - metadata:
        name: mongodb-config
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 10Gi

---

apiVersion: v1
kind: Service
metadata:
  name: mongodb-config-service
  labels:
    app: mongodb-config-service
spec:
  clusterIP: None
  ports:
    - port: 27019
      targetPort: 27019
      protocol: TCP
      name: mongodb-config
  selector:
    app: mongodb-config

---
{{- $shard_size := until (int .Values.mongodb.shard_size) }}
{{- range $shard_size }}
kind: StatefulSet
apiVersion: apps/v1
metadata:
  name: mongodb-shard-{{ . }}
  labels:
    app: mongodb-shard-{{ . }}
spec:
  serviceName: mongodb-shard-{{ . }}-service
  replicas: {{ $.Values.mongodb.replicas }}
  selector:
    matchLabels:
      app: mongodb-shard-{{ . }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: mongodb-shard-{{ . }}
    spec:
      containers:
        - name: mongodb-shard-{{ . }}
          image: mongo:8.0.4
          command:
            - mongod
            - "--shardsvr"
            - "--replSet"
            - shard-{{ . }}-rs
            - "--bind_ip_all"
          ports:
            - containerPort: 27018
              name: mongodb-shard-{{ . }}
          livenessProbe:
            tcpSocket:
              port: 27018
            initialDelaySeconds: 15
            periodSeconds: 10
          env:
            - name: MONGO_INITDB_ROOT_USERNAME
              value: {{ $.Values.mongodb.mongo_initdb_root_username }}
            - name: MONGO_INITDB_ROOT_PASSWORD
              value: {{ $.Values.mongodb.mongo_initdb_root_password }}
            - name: MONGO_INITDB_DATABASE
              value: {{ $.Values.mongodb.mongo_initdb_database }}
            - name: GLIBC_TUNABLES
              value: glibc.pthread.rseq=0
          volumeMounts:
            - name: mongodb-shard-{{ . }}
              mountPath: /data
          resources:
{{ toYaml $.Values.mongodb.shard_resources | indent 12 }}
  volumeClaimTemplates:
    - metadata:
        name: mongodb-shard-{{ . }}
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: {{ $.Values.mongodb.storage_resources }}

---

apiVersion: v1
kind: Service
metadata:
  name: mongodb-shard-{{ . }}-service
  labels:
    app: mongodb-shard-{{ . }}-service
spec:
  clusterIP: None
  ports:
    - port: 27018
      targetPort: 27018
      protocol: TCP
      name: mongodb-shard-{{ . }}
  selector:
    app: mongodb-shard-{{ . }}


---

{{- end }}

{{- $config_addr_list := list }}
{{- range until (int .Values.mongodb.replicas) }}
  {{- $config_addr_list = append $config_addr_list (printf "mongodb-config-%d.mongodb-config-service:27019" .) }}
{{- end }}

kind: Deployment
apiVersion: apps/v1
metadata:
  name: mongodb-mongos
  labels:
    app: mongodb-mongos
spec:
  replicas: {{ .Values.mongodb.mongos_replicas }}
  selector:
    matchLabels:
      app: mongodb-mongos
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  template:
    metadata:
      labels:
        app: mongodb-mongos
    spec:
      containers:
        - name: mongodb-mongos
          image: mongo:8.0.4
          command:
            - mongos
            - "--configdb"
            - "config-rs/{{- join "," $config_addr_list }}"
            - "--bind_ip_all"
          ports:
            - containerPort: 27017
              name: mongodb-mongos
          env:
            - name: MONGO_INITDB_ROOT_USERNAME
              value: {{ .Values.mongodb.mongo_initdb_root_username }}
            - name: MONGO_INITDB_ROOT_PASSWORD
              value: {{ .Values.mongodb.mongo_initdb_root_password }}
            - name: MONGO_INITDB_DATABASE
              value: {{ .Values.mongodb.mongo_initdb_database }}
          resources:
{{ toYaml $.Values.mongodb.mongos_resources | indent 12 }}


---

apiVersion: v1
kind: Service
metadata:
  name: mongodb-service
  labels:
    app: mongodb-service
spec:
{{ if .Values.debug }}
  type: LoadBalancer
  loadBalancerIP: {{ .Values.static_ip.mongodb }}
{{ end }}
  ports:
    - port: 27017
      targetPort: 27017
      protocol: TCP
      name: mongodb-mongos
  selector:
    app: mongodb-mongos
