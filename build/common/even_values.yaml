
mongodb:
  shard_size: 2
  replicas: 3
  mongos_replicas: 1
  mongo_initdb_root_username: ai-jarvis
  mongo_initdb_root_password: RGvfYe8LXrLuLhkU
  mongo_initdb_database: ai_jarvis

  shard_resources:
    requests:
      cpu: 125m
      memory: "1Gi"
    limits:
      cpu: 1
      memory: "2Gi"
  config_resources:
    requests:
      cpu: 125m
      memory: "1Gi"
    limits:
      cpu: 1
      memory: "2Gi"
  mongos_resources:
    requests:
      cpu: 250m
      memory: "1Gi"
    limits:
      cpu: 4
      memory: "8Gi"
  storage_resources: "100Gi"


searxng:
  replicas: 1
  resources:
    requests:
      cpu: 250m
      memory: "1Gi"
    limits:
      cpu: 2
      memory: "4Gi"


redis:
  init_password: 2dWD6qZT7CfTCCbJ
  resources:
    requests:
      cpu: 1
      memory: "1Gi"
    limits:
      cpu: 2
      memory: "4Gi"
  storage_resources: "10Gi"


debug: 0
init_gateway: 0
