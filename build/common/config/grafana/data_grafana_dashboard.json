{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "iteration": 1667344411089, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Amount spilled by dataset operators. DataContext.enable_get_object_locations_for_metrics must be set to True to report this metric", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 1, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_spilled_bytes{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Bytes Spilled: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Bytes Spilled", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Amount allocated by dataset operators.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_allocated_bytes{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Bytes Allocated: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Bytes Allocated", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Amount freed by dataset operators.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_freed_bytes{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Bytes Freed: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Bytes Freed", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Amount of memory store used by dataset operators.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_current_bytes{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Current Usage: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Object Store Memory", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Logical CPUs allocated to dataset operators.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 2}, "hiddenSeries": false, "id": 5, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_cpu_usage_cores{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "CPU Usage: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPUs (logical slots)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "cores", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Logical GPUs allocated to dataset operators.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 2}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_gpu_usage_cores{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "GPU Usage: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GPUs (logical slots)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "cores", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Bytes output per second by dataset operators.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 3}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_data_output_bytes{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}[1m])) by (dataset, operator)", "interval": "", "legendFormat": "Bytes Output / Second: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Bytes Output / Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "Bps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Total rows output per second by dataset operators.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 3}, "hiddenSeries": false, "id": 11, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_data_output_rows{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}[1m])) by (dataset, operator)", "interval": "", "legendFormat": "Rows Output / Second: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Rows Output / Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "rows/sec", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of input blocks received by operator per second.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}, "hiddenSeries": false, "id": 17, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_data_num_inputs_received{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}[1m])) by (dataset, operator)", "interval": "", "legendFormat": "Blocks Received / Second: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Input Blocks Received by Operator / Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "blocks/sec", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Byte size of input blocks received by operator per second.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}, "hiddenSeries": false, "id": 18, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_data_bytes_inputs_received{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}[1m])) by (dataset, operator)", "interval": "", "legendFormat": "Bytes Received / Second: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Input Bytes Received by Operator / Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "Bps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of input blocks that operator's tasks have finished processing per second.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 5}, "hiddenSeries": false, "id": 19, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_data_num_task_inputs_processed{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}[1m])) by (dataset, operator)", "interval": "", "legendFormat": "Blocks Processed / Second: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Input Blocks Processed by Tasks / Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "blocks/sec", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Byte size of input blocks that operator's tasks have finished processing per second.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 5}, "hiddenSeries": false, "id": 20, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_data_bytes_task_inputs_processed{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}[1m])) by (dataset, operator)", "interval": "", "legendFormat": "Bytes Processed / Second: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Input Bytes Processed by Tasks / Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "Bps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Byte size of input blocks passed to submitted tasks per second.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "hiddenSeries": false, "id": 21, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_data_bytes_inputs_of_submitted_tasks{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}[1m])) by (dataset, operator)", "interval": "", "legendFormat": "Bytes Submitted / Second: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Input Bytes Submitted to Tasks / Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "Bps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of output blocks generated by tasks per second.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "hiddenSeries": false, "id": 22, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_data_num_task_outputs_generated{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}[1m])) by (dataset, operator)", "interval": "", "legendFormat": "Blocks Generated / Second: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Blocks Generated by Tasks / Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "blocks/sec", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Byte size of output blocks generated by tasks per second.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 7}, "hiddenSeries": false, "id": 23, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_data_bytes_task_outputs_generated{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}[1m])) by (dataset, operator)", "interval": "", "legendFormat": "Bytes Generated / Second: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Bytes Generated by Tasks / Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "Bps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of rows in generated output blocks from finished tasks per second.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 7}, "hiddenSeries": false, "id": 24, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_data_rows_task_outputs_generated{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}[1m])) by (dataset, operator)", "interval": "", "legendFormat": "Rows Generated / Second: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Rows Generated by Tasks / Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "rows/sec", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of output blocks taken by downstream operators per second.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "hiddenSeries": false, "id": 25, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_data_num_outputs_taken{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}[1m])) by (dataset, operator)", "interval": "", "legendFormat": "Blocks Taken / Second: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Output Blocks Taken by Downstream Operators / Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "blocks/sec", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Byte size of output blocks taken by downstream operators per second.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "hiddenSeries": false, "id": 26, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_data_bytes_outputs_taken{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}[1m])) by (dataset, operator)", "interval": "", "legendFormat": "Bytes Taken / Second: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Output Bytes Taken by Downstream Operators / Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "Bps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of submitted tasks.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "hiddenSeries": false, "id": 29, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_num_tasks_submitted{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Submitted Tasks: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Submitted Tasks", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "tasks", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of running tasks.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "hiddenSeries": false, "id": 30, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_num_tasks_running{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Running Tasks: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Running Tasks", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "tasks", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of tasks that already have output.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "hiddenSeries": false, "id": 31, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_num_tasks_have_outputs{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Tasks with output blocks: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Tasks with output blocks", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "tasks", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of finished tasks.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "hiddenSeries": false, "id": 32, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_num_tasks_finished{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Finished Tasks: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Finished Tasks", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "tasks", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of failed tasks.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 11}, "hiddenSeries": false, "id": 33, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_num_tasks_failed{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Failed Tasks: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Failed Tasks", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "tasks", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Time spent generating blocks in tasks.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 11}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_block_generation_time{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Block Generation Time: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Block Generation Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "seconds", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Time spent in task submission backpressure.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "hiddenSeries": false, "id": 37, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_task_submission_backpressure_time{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Backpressure Time: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Task Submission Backpressure Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "seconds", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of blocks in operator's internal input queue", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "hiddenSeries": false, "id": 13, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_obj_store_mem_internal_inqueue_blocks{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Number of Blocks: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Operator Internal Inqueue Size (Blocks)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "blocks", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Byte size of input blocks in the operator's internal input queue.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 13}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_obj_store_mem_internal_inqueue{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Bytes Size: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Operator Internal Inqueue Size (Bytes)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of blocks in operator's internal output queue", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 13}, "hiddenSeries": false, "id": 15, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_obj_store_mem_internal_outqueue_blocks{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Number of Blocks: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Operator Internal Outqueue Size (Blocks)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "blocks", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Byte size of output blocks in the operator's internal output queue.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_obj_store_mem_internal_outqueue{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Bytes Size: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Operator Internal Outqueue Size (Bytes)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Byte size of input blocks used by pending tasks.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "hiddenSeries": false, "id": 34, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_obj_store_mem_pending_task_inputs{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Bytes Size: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Size of Blocks used in Pending Tasks (Bytes)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Byte size of freed memory in object store.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 15}, "hiddenSeries": false, "id": 35, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_obj_store_mem_freed{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Bytes Size: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Freed Memory in Object Store (Bytes)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Byte size of spilled memory in object store.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 15}, "hiddenSeries": false, "id": 36, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_obj_store_mem_spilled{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset, operator)", "interval": "", "legendFormat": "Bytes Size: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Spilled Memory in Object Store (Bytes)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Seconds spent in iterator initialization code", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "hiddenSeries": false, "id": 12, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_iter_initialize_seconds{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset)", "interval": "", "legendFormat": "Seconds: {{dataset}}, {{operator}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Iteration Initialization Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "seconds", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Seconds user thread is blocked by iter_batches()", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "hiddenSeries": false, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_iter_total_blocked_seconds{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset)", "interval": "", "legendFormat": "Seconds: {{dataset}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Iteration Blocked Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "seconds", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Seconds spent in user code", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_data_iter_user_seconds{dataset=~\"$DatasetID\",SessionName=~\"$SessionName\",ray_io_cluster=~\"$Cluster\",}) by (dataset)", "interval": "", "legendFormat": "Seconds: {{dataset}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Iteration User Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "seconds", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["rayVersion:2.42.1"], "templating": {"list": [{"current": {"selected": false}, "description": "Filter queries of a specific Prometheus type.", "hide": 2, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": ".+", "current": {"selected": false}, "datasource": "${datasource}", "definition": "label_values(ray_data_allocated_bytes{}, SessionName)", "description": "Filter queries to specific ray sessions.", "error": null, "hide": 0, "includeAll": true, "label": null, "multi": false, "name": "SessionName", "options": [], "query": {"query": "label_values(ray_data_allocated_bytes{}, SessionName)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(ray_data_allocated_bytes{}, dataset)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "DatasetID", "options": [], "query": {"query": "label_values(ray_data_allocated_bytes{}, dataset)", "refId": "Prometheus-Dataset-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false}, "datasource": "${datasource}", "definition": "label_values(ray_node_network_receive_speed{}, ray_io_cluster)", "description": "Filter queries to specific Ray clusters for KubeRay. When ingesting metrics across multiple ray clusters, the ray_io_cluster label should be set per cluster. For KubeRay users, this is done automaticaly with Prometheus PodMonitor.", "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "Cluster", "options": [], "query": {"query": "label_values(ray_node_network_receive_speed{}, ray_io_cluster)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "rayMeta": ["excludesSystemRoutes", "supportsGlobalFilterOverride"], "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Data Dashboard", "uid": "rayDataDashboard", "version": 1}