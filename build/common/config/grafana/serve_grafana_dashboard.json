{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "iteration": 1667344411089, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Aggregated utilization of all physical resources (CPU, GPU, memory, disk, or etc.) across the cluster. Ignores application variable.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 0, "y": 0, "w": 12, "h": 8}, "hiddenSeries": false, "id": 5, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "avg(ray_node_cpu_utilization{ray_io_cluster=~\"$Cluster\",})", "interval": "", "legendFormat": "CPU (physical)", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "sum(ray_node_gpus_utilization{ray_io_cluster=~\"$Cluster\",}) / on() (sum(autoscaler_cluster_resources{resource='GPU',ray_io_cluster=~\"$Cluster\",}) or vector(0))", "interval": "", "legendFormat": "GPU (physical)", "queryType": "randomWalk", "refId": "B"}, {"exemplar": true, "expr": "sum(ray_node_mem_used{ray_io_cluster=~\"$Cluster\",}) / on() (sum(ray_node_mem_total{ray_io_cluster=~\"$Cluster\",})) * 100", "interval": "", "legendFormat": "Memory (RAM)", "queryType": "randomWalk", "refId": "C"}, {"exemplar": true, "expr": "sum(ray_node_gram_used{ray_io_cluster=~\"$Cluster\",}) / on() (sum(ray_node_gram_available{ray_io_cluster=~\"$Cluster\",}) + sum(ray_node_gram_used{ray_io_cluster=~\"$Cluster\",})) * 100", "interval": "", "legendFormat": "GRAM", "queryType": "randomWalk", "refId": "D"}, {"exemplar": true, "expr": "sum(ray_object_store_memory{ray_io_cluster=~\"$Cluster\",}) / on() sum(ray_resources{Name=\"object_store_memory\",ray_io_cluster=~\"$Cluster\",}) * 100", "interval": "", "legendFormat": "Object Store Memory", "queryType": "randomWalk", "refId": "E"}, {"exemplar": true, "expr": "sum(ray_node_disk_usage{ray_io_cluster=~\"$Cluster\",}) / on() (sum(ray_node_disk_free{ray_io_cluster=~\"$Cluster\",}) + sum(ray_node_disk_usage{ray_io_cluster=~\"$Cluster\",})) * 100", "interval": "", "legendFormat": "Disk", "queryType": "randomWalk", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Cluster Utilization", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "%", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "QPS for each selected application.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 12, "y": 0, "w": 12, "h": 8}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_serve_num_http_requests_total{application=~\"$Application\",application!~\"\",route=~\"$HTTP_Route\",route!~\"/-/.*\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, route)", "interval": "", "legendFormat": "{{application, route}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "sum(rate(ray_serve_num_grpc_requests_total{application=~\"$Application\",application!~\"\",method=~\"$gRPC_Method\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, method)", "interval": "", "legendFormat": "{{application, method}}", "queryType": "randomWalk", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "QPS per application", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "qps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Error QPS for each selected application.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 0, "y": 1, "w": 12, "h": 8}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_serve_num_http_error_requests_total{application=~\"$Application\",application!~\"\",route=~\"$HTTP_Route\",route!~\"/-/.*\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, route)", "interval": "", "legendFormat": "{{application, route}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "sum(rate(ray_serve_num_grpc_error_requests_total{application=~\"$Application\",application!~\"\",method=~\"$gRPC_Method\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, method)", "interval": "", "legendFormat": "{{application, method}}", "queryType": "randomWalk", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error QPS per application", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "qps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Error QPS for each selected application.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 12, "y": 1, "w": 12, "h": 8}, "hiddenSeries": false, "id": 17, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_serve_num_http_error_requests_total{application=~\"$Application\",application!~\"\",route=~\"$HTTP_Route\",route!~\"/-/.*\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, route, error_code)", "interval": "", "legendFormat": "{{application, route, error_code}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "sum(rate(ray_serve_num_grpc_error_requests_total{application=~\"$Application\",application!~\"\",method=~\"$gRPC_Method\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, method, error_code)", "interval": "", "legendFormat": "{{application, method, error_code}}", "queryType": "randomWalk", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error QPS per application per error code", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "qps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "P50 latency for selected applications.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 0, "y": 2, "w": 8, "h": 8}, "hiddenSeries": false, "id": 12, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.5, sum(rate(ray_serve_http_request_latency_ms_bucket{application=~\"$Application\",application!~\"\",route=~\"$HTTP_Route\",route!~\"/-/.*\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, route, le))", "interval": "", "legendFormat": "{{application, route}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile(0.5, sum(rate(ray_serve_grpc_request_latency_ms_bucket{application=~\"$Application\",application!~\"\",method=~\"$gRPC_Method\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, method, le))", "interval": "", "legendFormat": "{{application, method}}", "queryType": "randomWalk", "refId": "B"}, {"exemplar": true, "expr": "histogram_quantile(0.5, sum(rate({__name__=~ \"ray_serve_(http|grpc)_request_latency_ms_bucket\",application=~\"$Application\",application!~\"\",ray_io_cluster=~\"$Cluster\",}[5m])) by (le))", "interval": "", "legendFormat": "Total", "queryType": "randomWalk", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "P50 latency per application", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "ms", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "P90 latency for selected applications.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 8, "y": 2, "w": 8, "h": 8}, "hiddenSeries": false, "id": 15, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.9, sum(rate(ray_serve_http_request_latency_ms_bucket{application=~\"$Application\",application!~\"\",route=~\"$HTTP_Route\",route!~\"/-/.*\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, route, le))", "interval": "", "legendFormat": "{{application, route}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile(0.9, sum(rate(ray_serve_grpc_request_latency_ms_bucket{application=~\"$Application\",application!~\"\",method=~\"$gRPC_Method\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, method, le))", "interval": "", "legendFormat": "{{application, method}}", "queryType": "randomWalk", "refId": "B"}, {"exemplar": true, "expr": "histogram_quantile(0.9, sum(rate({__name__=~ \"ray_serve_(http|grpc)_request_latency_ms_bucket|ray_serve_grpc_request_latency_ms_bucket\",application=~\"$Application\",application!~\"\",ray_io_cluster=~\"$Cluster\",}[5m])) by (le))", "interval": "", "legendFormat": "Total", "queryType": "randomWalk", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "P90 latency per application", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "ms", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "P99 latency for selected applications.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 16, "y": 2, "w": 8, "h": 8}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(ray_serve_http_request_latency_ms_bucket{application=~\"$Application\",application!~\"\",route=~\"$HTTP_Route\",route!~\"/-/.*\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, route, le))", "interval": "", "legendFormat": "{{application, route}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(ray_serve_grpc_request_latency_ms_bucket{application=~\"$Application\",application!~\"\",method=~\"$gRPC_Method\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, method, le))", "interval": "", "legendFormat": "{{application, method}}", "queryType": "randomWalk", "refId": "B"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate({__name__=~ \"ray_serve_(http|grpc)_request_latency_ms_bucket|ray_serve_grpc_request_latency_ms_bucket\",application=~\"$Application\",application!~\"\",ray_io_cluster=~\"$Cluster\",}[5m])) by (le))", "interval": "", "legendFormat": "Total", "queryType": "randomWalk", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "P99 latency per application", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "ms", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of replicas per deployment. Ignores \"Application\" variable.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 0, "y": 3, "w": 8, "h": 8}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_serve_deployment_replica_healthy{ray_io_cluster=~\"$Cluster\",}) by (application, deployment)", "interval": "", "legendFormat": "{{application, deployment}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Replicas per deployment", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "replicas", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "QPS for each deployment.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 8, "y": 3, "w": 8, "h": 8}, "hiddenSeries": false, "id": 13, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_serve_deployment_request_counter_total{application=~\"$Application\",application!~\"\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, deployment)", "interval": "", "legendFormat": "{{application, deployment}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "QPS per deployment", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "qps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Error QPS for each deplyoment.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 16, "y": 3, "w": 8, "h": 8}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_serve_deployment_error_counter_total{application=~\"$Application\",application!~\"\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, deployment)", "interval": "", "legendFormat": "{{application, deployment}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error QPS per deployment", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "qps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "P50 latency per deployment.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 0, "y": 4, "w": 8, "h": 8}, "hiddenSeries": false, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.5, sum(rate(ray_serve_deployment_processing_latency_ms_bucket{application=~\"$Application\",application!~\"\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, deployment, le))", "interval": "", "legendFormat": "{{application, deployment}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile(0.5, sum(rate(ray_serve_deployment_processing_latency_ms_bucket{application=~\"$Application\",application!~\"\",ray_io_cluster=~\"$Cluster\",}[5m])) by (le))", "interval": "", "legendFormat": "Total", "queryType": "randomWalk", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "P50 latency per deployment", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "ms", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "P90 latency per deployment.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 8, "y": 4, "w": 8, "h": 8}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.9, sum(rate(ray_serve_deployment_processing_latency_ms_bucket{application=~\"$Application\",application!~\"\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, deployment, le))", "interval": "", "legendFormat": "{{application, deployment}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile(0.9, sum(rate(ray_serve_deployment_processing_latency_ms_bucket{application=~\"$Application\",application!~\"\",ray_io_cluster=~\"$Cluster\",}[5m])) by (le))", "interval": "", "legendFormat": "Total", "queryType": "randomWalk", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "P90 latency per deployment", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "ms", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "P99 latency per deployment.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 16, "y": 4, "w": 8, "h": 8}, "hiddenSeries": false, "id": 11, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(ray_serve_deployment_processing_latency_ms_bucket{application=~\"$Application\",application!~\"\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, deployment, le))", "interval": "", "legendFormat": "{{application, deployment}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(ray_serve_deployment_processing_latency_ms_bucket{application=~\"$Application\",application!~\"\",ray_io_cluster=~\"$Cluster\",}[5m])) by (le))", "interval": "", "legendFormat": "Total", "queryType": "randomWalk", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "P99 latency per deployment", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "ms", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of requests queued per deployment. Ignores \"Application\" variable.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 0, "y": 5, "w": 8, "h": 8}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_serve_deployment_queued_queries{ray_io_cluster=~\"$Cluster\",}) by (application, deployment)", "interval": "", "legendFormat": "{{application, deployment}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Queue size per deployment", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "requests", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of nodes in this cluster. Ignores \"Application\" variable.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 8, "y": 5, "w": 8, "h": 8}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(autoscaler_active_nodes{ray_io_cluster=~\"$Cluster\",}) by (NodeType)", "interval": "", "legendFormat": "Active Nodes: {{NodeType}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "sum(autoscaler_recently_failed_nodes{ray_io_cluster=~\"$Cluster\",}) by (NodeType)", "interval": "", "legendFormat": "Failed Nodes: {{NodeType}}", "queryType": "randomWalk", "refId": "B"}, {"exemplar": true, "expr": "sum(autoscaler_pending_nodes{ray_io_cluster=~\"$Cluster\",}) by (NodeType)", "interval": "", "legendFormat": "Pending Nodes: {{NodeType}}", "queryType": "randomWalk", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "nodes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Network speed per node. Ignores \"Application\" variable.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"x": 16, "y": 5, "w": 8, "h": 8}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_node_network_receive_speed{ray_io_cluster=~\"$Cluster\",}) by (instance)", "interval": "", "legendFormat": "Recv: {{instance}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "sum(ray_node_network_send_speed{ray_io_cluster=~\"$Cluster\",}) by (instance)", "interval": "", "legendFormat": "Send: {{instance}}", "queryType": "randomWalk", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node network", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "Bps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "The number of ongoing requests in the HTTP Proxy.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 0, "y": 6, "w": 8, "h": 8}, "hiddenSeries": false, "id": 20, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "ray_serve_num_ongoing_http_requests{ray_io_cluster=~\"$Cluster\",}", "interval": "", "legendFormat": "Ongoing HTTP Requests", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Ongoing HTTP Requests", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "requests", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "The number of ongoing requests in the gRPC Proxy.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 8, "y": 6, "w": 8, "h": 8}, "hiddenSeries": false, "id": 21, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "ray_serve_num_ongoing_grpc_requests{ray_io_cluster=~\"$Cluster\",}", "interval": "", "legendFormat": "Ongoing gRPC Requests", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Ongoing gRPC Requests", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "requests", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "The number of request scheduling tasks in the router.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 16, "y": 6, "w": 8, "h": 8}, "hiddenSeries": false, "id": 22, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "ray_serve_num_scheduling_tasks{ray_io_cluster=~\"$Cluster\",}", "interval": "", "legendFormat": "Scheduling Tasks", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Scheduling Tasks", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "tasks", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "The number of request scheduling tasks in the router that are undergoing backoff.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 0, "y": 7, "w": 8, "h": 8}, "hiddenSeries": false, "id": 23, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "ray_serve_num_scheduling_tasks_in_backoff{ray_io_cluster=~\"$Cluster\",}", "interval": "", "legendFormat": "Scheduling Tasks in Backoff", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Scheduling Tasks in Backoff", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "tasks", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "The duration of the last control loop.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 8, "y": 7, "w": 8, "h": 8}, "hiddenSeries": false, "id": 24, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "ray_serve_controller_control_loop_duration_s{ray_io_cluster=~\"$Cluster\",}", "interval": "", "legendFormat": "Control Loop Duration", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Controller Control Loop Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "seconds", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "The number of control loops performed by the controller. Increases monotonically over the controller's lifetime.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 16, "y": 7, "w": 8, "h": 8}, "hiddenSeries": false, "id": 25, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "ray_serve_controller_num_control_loops{ray_io_cluster=~\"$Cluster\",}", "interval": "", "legendFormat": "Control Loops", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Number of Control Loops", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "loops", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["rayVersion:2.42.1"], "templating": {"list": [{"current": {"selected": false}, "description": "Filter queries of a specific Prometheus type.", "hide": 2, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(ray_serve_deployment_replica_healthy{}, application)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "Application", "options": [], "query": {"query": "label_values(ray_serve_deployment_replica_healthy{}, application)", "refId": "Prometheus-Instance-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(ray_serve_num_http_requests_total{}, route)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "HTTP Route", "multi": true, "name": "HTTP_Route", "options": [], "query": {"query": "label_values(ray_serve_num_http_requests_total{}, route)", "refId": "Prometheus-Instance-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(ray_serve_num_grpc_requests{}, method)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "gRPC Service Method", "multi": true, "name": "gRPC_Method", "options": [], "query": {"query": "label_values(ray_serve_num_grpc_requests{}, method)", "refId": "Prometheus-Instance-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false}, "datasource": "${datasource}", "definition": "label_values(ray_node_network_receive_speed{}, ray_io_cluster)", "description": "Filter queries to specific Ray clusters for KubeRay. When ingesting metrics across multiple ray clusters, the ray_io_cluster label should be set per cluster. For KubeRay users, this is done automaticaly with Prometheus PodMonitor.", "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "Cluster", "options": [], "query": {"query": "label_values(ray_node_network_receive_speed{}, ray_io_cluster)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "rayMeta": ["excludesSystemRoutes", "supportsGlobalFilterOverride"], "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Serve <PERSON>", "uid": "rayServeDashboard", "version": 1}