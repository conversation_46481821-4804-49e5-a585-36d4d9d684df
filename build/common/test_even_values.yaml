
mongodb:
  shard_size: 2
  replicas: 3
  mongos_replicas: 1
  mongo_initdb_root_username: ai-jarvis
  mongo_initdb_root_password: kQ8hD2kT3aC4mC4
  mongo_initdb_database: ai_jarvis

  shard_resources:
    requests:
      cpu: 125m
      memory: "1Gi"
    limits:
      cpu: 125m
      memory: "1Gi"
  config_resources:
    requests:
      cpu: 125m
      memory: "1Gi"
    limits:
      cpu: 125m
      memory: "1Gi"
  mongos_resources:
    requests:
      cpu: 250m
      memory: "1Gi"
    limits:
      cpu: 250m
      memory: "2Gi"
  storage_resources: "1Gi"


searxng:
  replicas: 1
  resources:
    requests:
      cpu: 250m
      memory: "1Gi"
    limits:
      cpu: 250m
      memory: "1Gi"


redis:
  init_password: 3N8yQi24Z6mBrcjd
  resources:
    requests:
      cpu: 1
      memory: "1Gi"
    limits:
      cpu: 2
      memory: "4Gi"
  storage_resources: "10Gi"


prometheus:
  storage_resources: "10Gi"

jarvis:
  service: ai-jarvis-service.test-ai.svc.cluster.local

debug: 0

init_gateway: 0
