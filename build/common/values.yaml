# Default values for common.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

mongodb:
  shard_size: 4
  replicas: 3
  mongos_replicas: 2
  mongo_initdb_root_username: ai-jarvis
  mongo_initdb_root_password: kQ8hD2kT3aC4mC4
  mongo_initdb_database: ai_jarvis

  shard_resources:
    requests:
      cpu: 1
      memory: "1Gi"
    limits:
      cpu: 2
      memory: "2Gi"
  config_resources:
    requests:
      cpu: 1
      memory: "1Gi"
    limits:
      cpu: 2
      memory: "2Gi"
  mongos_resources:
    requests:
      cpu: 1
      memory: "1Gi"
    limits:
      cpu: 2
      memory: "2Gi"
  storage_resources: "10Gi"


searxng:
  replicas: 2
  resources:
    requests:
      cpu: 1
      memory: "1Gi"
    limits:
      cpu: 2
      memory: "2Gi"

redis:
  init_password: 3N8yQi24Z6mBrcjd
  resources:
    requests:
      cpu: 1
      memory: "1Gi"
    limits:
      cpu: 2
      memory: "4Gi"
  storage_resources: "10Gi"


debug: 0

static_ip:
  ai_jarvis: **************
