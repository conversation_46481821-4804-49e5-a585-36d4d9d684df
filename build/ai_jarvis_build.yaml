steps:
  - name: 'gcr.io/cloud-builders/docker'
    args: [ 'build', '-t', 'us-central1-docker.pkg.dev/${PROJECT_ID}/ai/ai-jarvis:${SHORT_SHA}', '.' ]
  - name: 'gcr.io/cloud-builders/docker'
    args: [ 'push',  'us-central1-docker.pkg.dev/${PROJECT_ID}/ai/ai-jarvis:${SHORT_SHA}' ]
  - name: 'gcr.io/$PROJECT_ID/helm'
    args: [ 'upgrade', '--install', 'ai-jarvis', 'build/ai-jarvis', '--set', 'image=us-central1-docker.pkg.dev/even-tp-46351/ai/ai-jarvis:${SHORT_SHA}', '--debug' ]
    env:
      - 'CLOUDSDK_COMPUTE_REGION=us-central1'
      - 'CLOUDSDK_CONTAINER_CLUSTER=ai-jarvis'
options:
  logging: CLOUD_LOGGING_ONLY
images:
  - us-central1-docker.pkg.dev/${PROJECT_ID}/ai/ai-jarvis