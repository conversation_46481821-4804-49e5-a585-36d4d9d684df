version: '3.9'

services:
  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: root

  redis:
    image: redis:latest
    ports:
      - "16379:6379"

  mysql:
    image: mysql:latest
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: glass

  etcd:
    image: bitnami/etcd:latest
    ports:
      - "2379:2379"
      - "2380:2380"
    environment:
      - ALLOW_NONE_AUTHENTICATION=yes
      - ETCD_ENABLE_V2=true
      - ALLOW_NONE_AUTHENTICATION=yes 
      - ETCD_LISTEN_CLIENT_URLS=http://0.0.0.0:12379 
    network_mode: host


  grafana:
    image: grafana/grafana-oss
    ports:
      - "3000:3000"

  loki:
    image: grafana/loki:latest
    ports:
      - "3100:3100"
    volumes:
      - ./loki.yml:/etc/loki/local-config.yaml
      - loki_data:/data/loki

  tempo:
    image: grafana/tempo:latest
    command: ["-config.file=/etc/tempo.yaml"]
    volumes:
      - ./tempo.yaml:/etc/tempo.yaml
      - tempo_data:/data/tempo
    ports:
      - "3200:3200" # tempo http
      - "4317:4317" # OTLP gRPC
      - "4318:4318" # OTLP HTTP

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

volumes:
  loki_data:
    driver: local
  tempo_data:
    driver: local
