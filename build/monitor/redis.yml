apiVersion: monitoring.coreos.com/v1
kind: PodMonitor
metadata:
  name: ai-jarvis-redis-metrics
  namespace: monitoring  # Prometheus Operator 所在 namespace
  labels:
    release: prometheus  # 确保和 Prometheus CR 的 selector 匹配
spec:
  selector:
    matchLabels:
      app: redis
  podMetricsEndpoints:
    - port: metrics
      path: /metrics
      interval: 15s
  namespaceSelector:
    any: true
    matchNames:
      - ai-middleware
      - test-ai-middleware
