# 部署文档


## MongoDB 初始化脚本

### MongodbConfig 容器
```bash
mongosh --port 27019

rs.initiate( {
   _id : "config-rs",
   members: [
      { _id: 0, host: "mongodb-config-0.mongodb-config-service:27019", "priority": 1 },
      { _id: 1, host: "mongodb-config-1.mongodb-config-service:27019", "priority": 1 },
      { _id: 2, host: "mongodb-config-2.mongodb-config-service:27019", "priority": 2 }
   ]
})
```


### MongodbShard 容器

#### Shard-0
```bash
mongosh --port 27018

rs.initiate({
  _id: "shard-0-rs",
  members: [
    { _id: 0, host: "mongodb-shard-0-0.mongodb-shard-0-service:27018", priority: 1 },
    { _id: 1, host: "mongodb-shard-0-1.mongodb-shard-0-service:27018", priority: 1 },
    { _id: 2, host: "mongodb-shard-0-2.mongodb-shard-0-service:27018", priority: 2 }
  ]
})
```


### Shard-1
```bash
mongosh --port 27018

rs.initiate({
  _id: "shard-1-rs",
  members: [
    { _id: 0, host: "mongodb-shard-1-0.mongodb-shard-1-service:27018", priority: 1 },
    { _id: 1, host: "mongodb-shard-1-1.mongodb-shard-1-service:27018", priority: 1 },
    { _id: 2, host: "mongodb-shard-1-2.mongodb-shard-1-service:27018", priority: 2 }
  ]
})
```

### Mongos 初始化
```bash
mongosh --port 27017

sh.addShard("shard-0-rs/mongodb-shard-0-0.mongodb-shard-0-service:27018,mongodb-shard-0-1.mongodb-shard-0-service:27018,mongodb-shard-0-2.mongodb-shard-0-service:27018")
sh.addShard("shard-1-rs/mongodb-shard-1-0.mongodb-shard-1-service:27018,mongodb-shard-1-1.mongodb-shard-1-service:27018,mongodb-shard-1-2.mongodb-shard-1-service:27018")
```


### mongos 初始化权限& DB
```bash
mongosh 



use admin

db.createUser({
  user: "ai-jarvis",
  pwd: "kQ8hD2kT3aC4mC4",
  roles: [ { role: "userAdminAnyDatabase", db: "admin" } ]
})

```

## MongoDB
假设集群中config和shard的副本数为3，数据分片数为4。

<font color='red'>副本数必须为奇数</font>
1. 在mongodb-config-0内执行
```bash
mongosh --port 27019

rs.initiate( {
   _id : "config-rs",
   members: [
      { _id: 0, host: "mongodb-config-0.mongodb-config-service:27019", "priority": 1 },
      { _id: 1, host: "mongodb-config-1.mongodb-config-service:27019", "priority": 1 },
      { _id: 2, host: "mongodb-config-2.mongodb-config-service:27019", "priority": 2 }
   ]
})
```

2. 在每个shard的第0个副本，比如shard=1时，在mongodb-shard-1-0上执行：
```bash
mongosh --port 27018

rs.initiate({
  _id: "shard-1-rs",
  members: [
    { _id: 0, host: "mongodb-shard-1-0.mongodb-shard-1-service:27018", priority: 1 },
    { _id: 1, host: "mongodb-shard-1-1.mongodb-shard-1-service:27018", priority: 1 },
    { _id: 2, host: "mongodb-shard-1-2.mongodb-shard-1-service:27018", priority: 2 }
  ]
})
```

3. 到mongodb-mongos的任意pod上逐个添加分片:
```bash
mongosh --port 27019
sh.addShard("shard-0-rs/mongodb-shard-0-0.mongodb-shard-0-service:27018,mongodb-shard-0-1.mongodb-shard-0-service:27018,mongodb-shard-0-2.mongodb-shard-0-service:27018")
sh.addShard("shard-1-rs/mongodb-shard-1-0.mongodb-shard-1-service:27018,mongodb-shard-1-1.mongodb-shard-1-service:27018,mongodb-shard-1-2.mongodb-shard-1-service:27018")
sh.addShard("shard-2-rs/mongodb-shard-2-0.mongodb-shard-2-service:27018,mongodb-shard-2-1.mongodb-shard-2-service:27018,mongodb-shard-2-2.mongodb-shard-2-service:27018")
sh.addShard("shard-3-rs/mongodb-shard-3-0.mongodb-shard-3-service:27018,mongodb-shard-3-1.mongodb-shard-3-service:27018,mongodb-shard-3-2.mongodb-shard-3-service:27018")
```

4. 相关账号初始化
创建管理账号
```bash
mongosh
use admin
db.createUser({
  user: "<admin_name>",
  pwd: "<admin_pwd>",
  roles: [ { role: "userAdminAnyDatabase", db: "admin" } ]
})
exit
```

使用管理账号登陆后，创建应用账号
```bash
mongosh -u <admin_name> -p <admin_pwd>
use ai-jarvis
db.createUser({
  user: "<app_name>",
  pwd: "<app_pwd>",
  roles: [ { role: "readWrite", db: "ai-jarvis" } ]
})
```

给jarvis_chat_message创建index
```bash
use ai-jarvis
db.jarvis_chat_message.createIndex({ session_id: 1, role: 1, user_id: 1})
db.jarvis_chat_message.createIndex({ session_id: 1, user_id: 1})
db.jarvis_chat_session.createIndex({ session_id: 1, role: 1, user_id: 1})
db.jarvis_chat_session.createIndex({ session_id: 1, user_id: 1})
exit
```



## Grafana
假设项目ID为even-tp-46351，命名空间为default
```bash
export PROJECT_ID=even-tp-46351
export NAMESPACE_NAME=default
export SERVER_ACCOUNT=monitor
```
1. 创建google cloud监控账号
```bash
gcloud config set project $PROJECT_ID \
&&
gcloud iam service-accounts create $SERVER_ACCOUNT \
&&
gcloud iam service-accounts add-iam-policy-binding \
  --role roles/iam.workloadIdentityUser \
  --member "serviceAccount:$PROJECT_ID.svc.id.goog[NAMESPACE_NAME/default]" \
  $SERVER_ACCOUNT@$PROJECT_ID.iam.gserviceaccount.com \
&&
kubectl annotate serviceaccount \
  --namespace $NAMESPACE_NAME \
  default \
  iam.gke.io/gcp-service-account=$SERVER_ACCOUNT@$PROJECT_ID.iam.gserviceaccount.com
```

2. 为账号授权
```bash
gcloud projects add-iam-policy-binding PROJECT_ID \
  --member=serviceAccount:$SERVER_ACCOUNT@PROJECT_ID.iam.gserviceaccount.com \
  --role=roles/$SERVER_ACCOUNTing.viewer \
&& \
gcloud projects add-iam-policy-binding PROJECT_ID \
  --member=serviceAccount:$SERVER_ACCOUNT@PROJECT_ID.iam.gserviceaccount.com \
  --role=roles/iam.serviceAccountTokenCreator
```

3. 在grafana里添加服务账号、添加prometheus数据源，同时在编辑页面的url得到DATASOURCE_UIDS、添加access token
4. 向google cloud添加定时任务同步OAuth，假设得到的DATASOURCE_UIDS为eed0wyy9i96o0e
```bash
PROJECT_ID=even-tp-46351 # The Project ID from Step 1.
GRAFANA_API_ENDPOINT=http://grafana-service.default.svc.cluster.local:3000 # The Grafana instance URL from step 2. This is a URL. Include "http://" or "https://".                   
DATASOURCE_UIDS=fed4ef06ud1q8a # The Grafana data source UID from step 3. This is not a URL.
GRAFANA_API_TOKEN=glsa_rAO3VhE7TqFRSrTYthe9CxsUOqOwUrqa_ed9749c0 # The Grafana service account token from step 4.

curl https://raw.githubusercontent.com/GoogleCloudPlatform/prometheus-engine/0ca68f91fedb8ab9fc5bc6871c3b100dd602e32b/cmd/datasource-syncer/datasource-syncer.yaml \
| sed 's|$DATASOURCE_UIDS|'"$DATASOURCE_UIDS"'|; s|$GRAFANA_API_ENDPOINT|'"$GRAFANA_API_ENDPOINT"'|; s|$GRAFANA_API_TOKEN|'"$GRAFANA_API_TOKEN"'|; s|$PROJECT_ID|'"$PROJECT_ID"'|;' \
| kubectl -n $NAMESPACE_NAME apply -f -
```
5. 检查OAuth是否有报错
```bash
kubectl logs job.batch/datasource-syncer-init
```
